#!/bin/bash

# 测试 session_exec 和 session_copyin 接口的脚本

BASE_URL="http://localhost:8080/api/v1/mcp"

echo "=== 测试 Session Exec 接口 ==="

# 测试 session_exec 接口
echo "1. 测试 session_exec 接口（使用不存在的 session_id）"
curl -X POST "${BASE_URL}/session/exec" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": 99999,
    "command": ["echo", "hello world"]
  }' | jq .

echo -e "\n2. 测试 session_exec 接口（缺少必要参数）"
curl -X POST "${BASE_URL}/session/exec" \
  -H "Content-Type: application/json" \
  -d '{
    "command": ["echo", "hello world"]
  }' | jq .

echo -e "\n=== 测试 Session Copyin 接口 ==="

# 测试 session_copyin 接口
echo "3. 测试 session_copyin 接口（使用不存在的 session_id）"
curl -X POST "${BASE_URL}/session/copyin" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": 99999,
    "files": [
      {
        "file_url": "https://example.com/test.txt",
        "dest_path": "/tmp/test.txt"
      }
    ]
  }' | jq .

echo -e "\n4. 测试 session_copyin 接口（缺少必要参数）"
curl -X POST "${BASE_URL}/session/copyin" \
  -H "Content-Type: application/json" \
  -d '{
    "files": [
      {
        "file_url": "https://example.com/test.txt",
        "dest_path": "/tmp/test.txt"
      }
    ]
  }' | jq .

echo -e "\n5. 测试 session_copyin 接口（空文件列表）"
curl -X POST "${BASE_URL}/session/copyin" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": 1,
    "files": []
  }' | jq .

echo -e "\n测试完成！"
