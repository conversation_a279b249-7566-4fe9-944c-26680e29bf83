# Reward 字段 JSON 化改造文档

## 改造概述

本次改造将数据库表 `obj_session` 中的 `reward` 字段从 `TEXT` 类型改为 `JSON` 类型，用于存储 k8sexecute 接口的返回结果。新的 JSON 结构包含执行返回码以及标准输出和标准错误的 BOS 永久链接。

## 修改内容

### 1. 数据库 Schema 修改

**文件**: `script/db/db.sql`

- 将 `reward` 字段类型从 `TEXT` 改为 `JSON`
- 更新字段注释，说明存储 k8sexecute 接口返回结果

```sql
-- 修改前
reward TEXT NULL COMMENT '奖励值，计算完成后存储',

-- 修改后  
reward JSON NULL COMMENT '奖励值JSON对象，存储k8sexecute接口返回结果，包含code、stderr_url、stdout_url字段',
```

### 2. Go 结构体定义修改

**文件**: `model/dao/session/def.go`

- 修改 `ObjSession` 结构体中的 `Reward` 字段类型从 `*string` 改为 `dao.JSONData`
- 新增 `RewardData` 结构体定义，用于规范 JSON 数据格式

```go
// 修改前
Reward *string `gorm:"column:reward" json:"reward"`

// 修改后
Reward dao.JSONData `gorm:"column:reward" json:"reward"`

// 新增结构体定义
type RewardData struct {
	Code      int    `json:"code"`       // k8s执行返回码
	StderrURL string `json:"stderr_url"` // 标准错误输出内容上传到BOS后的永久URL
	StdoutURL string `json:"stdout_url"` // 标准输出内容上传到BOS后的永久URL
}
```

### 3. 奖励计算器修改

**文件**: `library/reward_calculator/reward_calculator.go`

- 修改 `CalculateReward` 方法，不再将 stdout 和 stderr 合并
- 分别将 stderr 和 stdout 内容上传到 BOS
- 返回包含 `code`、`stderr_url`、`stdout_url` 的 JSON 字符串

**主要变更**:
- 为 stderr 和 stdout 分别生成 BOS 存储路径
- 使用 `RewardData` 结构体构建返回数据
- 将结果序列化为 JSON 字符串返回

### 4. Session 停止逻辑修改

**文件**: `model/service/session/session_stop.go`

- 修改 `asyncStopSession` 方法中的奖励计算逻辑
- 确保将 JSON 格式的奖励数据正确存储到数据库

```go
// 修改前
rewardBosURL, err := calculator.CalculateReward(ctx, session, "", 300)
// ...
"reward": rewardBosURL,

// 修改后
rewardJSON, err := calculator.CalculateReward(ctx, session, "", 300)
// ...
"reward": rewardJSON,
```

### 5. 奖励查询接口修改

**文件**: `model/service/calcreward/calculate_reward.go`

- 修改 `CalculateRewardOutputData` 结构体，添加 `ExecCode` 和 `ErrLog` 字段
- 修改处理 `session.Reward` 字段的逻辑，从BOS拉取实际内容返回
- 保留 `downloadFromURL` 方法用于从BOS下载内容

**主要变更**:
- `reward` 字段：从BOS拉取的stdout实际内容（字符串）
- `exec_code` 字段：k8s执行返回码（整数）
- `err_log` 字段：从BOS拉取的stderr实际内容（字符串）
- 解析存储在数据库中的JSON数据，提取BOS链接
- 通过HTTP请求从BOS下载实际的stdout和stderr内容

**返回数据格式**:
```json
{
  "reward": "实际的stdout内容",
  "exec_code": 0,
  "err_log": "实际的stderr内容"
}
```

## JSON 数据格式

新的 reward 字段存储的 JSON 对象格式如下：

```json
{
  "code": 0,
  "stderr_url": "https://example.bcebos.com/calculate/reward/session_code_20241215_143022_stderr.log",
  "stdout_url": "https://example.bcebos.com/calculate/reward/session_code_20241215_143022_stdout.log"
}
```

### 字段说明

- `code`: k8s 执行命令的返回码（整数）
- `stderr_url`: 标准错误输出内容上传到 BOS 后的永久访问链接（字符串）
- `stdout_url`: 标准输出内容上传到 BOS 后的永久访问链接（字符串）

## 向后兼容性

- 数据库字段类型从 TEXT 改为 JSON，MySQL 会自动处理类型转换
- 现有的空值（NULL）不受影响
- 接口返回格式保持 JSON 字符串，客户端无需修改

## 注意事项

1. **BOS 存储**: stderr 和 stdout 内容分别存储为独立的 BOS 对象，便于独立访问
2. **文件命名**: BOS 对象使用 `session_code_timestamp_stderr.log` 和 `session_code_timestamp_stdout.log` 格式命名
3. **错误处理**: 如果 BOS 上传失败，整个奖励计算过程会失败并返回错误
4. **空内容处理**: 如果 stderr 或 stdout 为空，对应的 URL 字段将为空字符串

## 涉及的接口

- `POST /api/session_stop`: Session 停止接口，会异步计算并存储奖励数据到数据库的reward字段（JSON格式）
- `POST /api/calc_reward`: 奖励查询接口，从数据库读取JSON格式的奖励数据，然后从BOS拉取实际内容返回

### calc_reward 接口返回格式

```json
{
  "reward": "实际的stdout内容（从BOS拉取）",
  "exec_code": 0,
  "err_log": "实际的stderr内容（从BOS拉取）",
  "session_status": "running"  // 可选字段，仅在特定状态下返回
}
```

**字段说明**:
- `reward`: 从BOS拉取的stdout实际内容，字符串类型
- `exec_code`: k8s执行命令的返回码，整数类型，与数据库中存储的code字段对应
- `err_log`: 从BOS拉取的stderr实际内容，字符串类型，与数据库中存储的stderr_url对应
- `session_status`: 会话状态，仅在特定情况下返回（如stopping状态）