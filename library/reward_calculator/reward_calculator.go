package reward_calculator

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	dao_rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// RewardCalculator 奖励计算器
type RewardCalculator struct{}

// NewRewardCalculator 创建奖励计算器实例
func NewRewardCalculator() *RewardCalculator {
	return &RewardCalculator{}
}

// CalculateReward 计算奖励值
// 这是一个同步方法，会执行实际的奖励计算逻辑
// 返回值：BOS下载链接（如果数据较大）或原始数据（如果数据较小或BOS上传失败）
func (rc *RewardCalculator) CalculateReward(ctx context.Context, session *dao_session.ObjSession, cmd string, timeoutSecs int) (string, error) {
	if cmd == "" {
		reward_cmd, err := getRewardCmd(ctx, session)
		if err != nil {
			return "", err
		}
		cmd = reward_cmd
	}
	if cmd == "" {
		return "", fmt.Errorf("未找到reward命令")
	}
	//TODO 确认下使用哪种
	// cmd 按照空格切分成数组
	// cmdArr := strings.Split(cmd, " ")
	cmdArr := []string{"sh", "-c", cmd}
	// 计算奖励
	cmdReq := dao_rpc_k8s_proxy.ExecCommandRequest{
		SourceType: rcc.GetValue("k8s_proxy.init.source_type", "mcp"),
		Command:    cmdArr,
		JobName:    session.JobID,
		Timeout:    timeoutSecs,
	}
	rewardRet, err := dao_rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, &cmdReq)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("执行reward命令失败: %v", err))
		return "", err
	}

	// 构建奖励数据结构
	rewardData := dao_session.RewardData{
		Code:    rewardRet.Code,
		Message: rewardRet.Message,
	}

	// 生成时间戳用于文件命名
	timestamp := time.Now().Format("20060102_150405")

	// 上传stderr到BOS（如果有内容）
	if rewardRet.Stderr != "" {
		stderrKey := fmt.Sprintf("calculate/reward/%s_%s_stderr.log", session.SessionCode, timestamp)
		err = dao_rpc_bos.UploadObjectFromString(ctx, stderrKey, rewardRet.Stderr, "text/plain; charset=utf-8")
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("上传stderr到BOS失败: %v", err))
			return "", err
		}
		rewardData.StderrURL = dao_rpc_bos.GenerateVisitURL(ctx, stderrKey, -1)
	}

	// 上传stdout到BOS（如果有内容）
	if rewardRet.Stdout != "" {
		stdoutKey := fmt.Sprintf("calculate/reward/%s_%s_stdout.log", session.SessionCode, timestamp)
		err = dao_rpc_bos.UploadObjectFromString(ctx, stdoutKey, rewardRet.Stdout, "text/plain; charset=utf-8")
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("上传stdout到BOS失败: %v", err))
			return "", err
		}
		rewardData.StdoutURL = dao_rpc_bos.GenerateVisitURL(ctx, stdoutKey, -1)
	}

	// 将奖励数据序列化为JSON字符串
	rewardJSON, err := json.Marshal(rewardData)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("序列化奖励数据失败: %v", err))
		return "", err
	}

	resource.LoggerService.Notice(ctx, fmt.Sprintf("奖励数据计算完成: session_code=%s, code=%d", session.SessionCode, rewardRet.Code))
	return string(rewardJSON), nil

}

// // CalculateRewardAsync 异步计算奖励值并更新到数据库
// // 这个方法会在goroutine中调用，用于异步计算奖励
// func (rc *RewardCalculator) CalculateRewardAsync(ctx context.Context, sessionID int64, session *dao_session.ObjSession, cmd string) {
// 	resource.LoggerService.Notice(ctx, fmt.Sprintf("开始异步计算奖励: session_id=%d", sessionID))

// 	// 计算奖励值
// 	rewardValue, err := rc.CalculateReward(ctx, session, cmd)
// 	if err != nil {
// 		resource.LoggerService.Warning(ctx, fmt.Sprintf("计算奖励失败: session_id=%d, error=%v", sessionID, err))
// 		// 计算失败，将状态恢复为running
// 		updateErr := dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
// 			"container_status": dao_session.ContainerStatusRunning,
// 		})
// 		if updateErr != nil {
// 			resource.LoggerService.Warning(ctx, fmt.Sprintf("恢复Session状态为running失败: session_id=%d, error=%v", sessionID, updateErr))
// 		}
// 		return
// 	}

// 	// 更新reward字段和状态
// 	err = dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
// 		"reward":           rewardValue,
// 		"container_status": dao_session.ContainerStatusRunning, // 计算完成后恢复为running状态
// 	})
// 	if err != nil {
// 		resource.LoggerService.Warning(ctx, fmt.Sprintf("更新reward字段失败: session_id=%d, error=%v", sessionID, err))
// 		return
// 	}

// 	resource.LoggerService.Notice(ctx, fmt.Sprintf("异步计算奖励完成: session_id=%d, reward=%s", sessionID, rewardValue))
// }

func getRewardCmd(ctx context.Context, session *dao_session.ObjSession) (string, error) {

	// 获取环境配置
	env, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, session.EnvID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询MCP环境失败: %v", err))
		return "", err
	}
	if env.EnvDependency == nil {
		return "", fmt.Errorf("环境依赖配置为空")
	}

	// 从JSONData中提取environment_dependency
	envDependencyData, ok := env.EnvDependency["environment_dependency"]
	if !ok {
		return "", fmt.Errorf("环境依赖配置中没有environment_dependency字段")
	}

	// 将interface{}转换为环境依赖列表
	var envDeps []struct {
		Path    string `json:"path"`
		Type    string `json:"type"`
		Name    string `json:"name"`
		Content string `json:"content"`
	}
	// 先转换为JSON字节，再解析
	envDepsBytes, err := json.Marshal(envDependencyData)
	if err != nil {
		return "", fmt.Errorf("序列化环境依赖配置失败: %v", err)
	}
	err = json.Unmarshal(envDepsBytes, &envDeps)
	if err != nil {
		return "", fmt.Errorf("解析环境依赖配置失败: %v", err)
	}

	reward_cmd := ""
	for _, item := range envDeps {
		if item.Type == "delay_cmd" && item.Name == "reward" {
			reward_cmd = item.Content
			break
		}
	}
	return reward_cmd, nil
}
