package errcode

const (
	ProjectBase = 33000

	SysBase                = 0
	SQLSelectError         = 1 + SysBase + ProjectBase
	SQLSelectCountError    = 2 + SysBase + ProjectBase
	SQLUpdateError         = 3 + SysBase + ProjectBase
	SQLInsertError         = 4 + SysBase + ProjectBase
	SQLDeleteError         = 5 + SysBase + ProjectBase
	SysFileReadError       = 6 + SysBase + ProjectBase
	SysFileOpenError       = 7 + SysBase + ProjectBase
	SysFileRemoveError     = 8 + SysBase + ProjectBase
	SysFileCreateError     = 9 + SysBase + ProjectBase
	SysDirCreateError      = 10 + SysBase + ProjectBase
	SysFileNotInit         = 11 + SysBase + ProjectBase
	SysFilePermissionError = 12 + SysBase + ProjectBase
	BRCCGetError           = 13 + SysBase + ProjectBase
	InvalidInput           = 14 + SysBase + ProjectBase
	UserInputError         = 15 + SysBase + ProjectBase

	McpServerBase       = 100
	McpServerInitFailed = 1 + McpServerBase + ProjectBase
	McpServerNotFound   = 2 + McpServerBase + ProjectBase

	EnvironmentBase       = 200
	EnvironmentInitFailed = 1 + EnvironmentBase + ProjectBase
	EnvironmentNotFound   = 2 + EnvironmentBase + ProjectBase

	BosBase              = 300
	BosUploadObjectError = 1 + BosBase + ProjectBase
	BosGetObjectError    = 2 + BosBase + ProjectBase
	BosDeleteObjectError = 3 + BosBase + ProjectBase
	BosGetFileMetaError  = 4 + BosBase + ProjectBase

	K8sContainerBase        = 400
	K8sContainerCreateError = 1 + K8sContainerBase + ProjectBase
	K8sContainerStartError  = 2 + K8sContainerBase + ProjectBase
	K8sContainerStopError   = 3 + K8sContainerBase + ProjectBase
	K8sContainerDeleteError = 4 + K8sContainerBase + ProjectBase
	K8sContainerGetError    = 5 + K8sContainerBase + ProjectBase
	K8sContainerListError   = 6 + K8sContainerBase + ProjectBase
	K8sRateLimitError       = 7 + K8sContainerBase + ProjectBase
	K8sJobLimitError        = 8 + K8sContainerBase + ProjectBase
	K8sExecCommandError     = 9 + K8sContainerBase + ProjectBase

	ToolCallTaskBase         = 500
	ToolCallTaskExecuteError = 1 + ToolCallTaskBase + ProjectBase
	ToolCallTaskTimeoutError = 2 + ToolCallTaskBase + ProjectBase
	ToolCallIDDuplicate      = 3 + ToolCallTaskBase + ProjectBase
	ToolCallTaskNotFound     = 4 + ToolCallTaskBase + ProjectBase

	SessionBase          = 600
	SessionInitFailed    = 1 + SessionBase + ProjectBase
	SessionStopFailed    = 2 + SessionBase + ProjectBase
	SessionTimeoutFailed = 3 + SessionBase + ProjectBase
	SessionFailed        = 4 + SessionBase + ProjectBase
	SessionNotFound      = 5 + SessionBase + ProjectBase
	SesssionIsStopped    = 6 + SessionBase + ProjectBase

	ImageBase     = 700
	ImageNotFound = 1 + ImageBase + ProjectBase
)
