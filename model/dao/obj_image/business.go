package dao

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

var ObjImageBusinessIns = ObjImageBusiness{}

type ObjImageBusiness struct{}

// Insert 插入镜像记录
func (bus ObjImageBusiness) Insert(ctx context.Context, data *ObjImage) (int64, error) {
	// 如果image_id为空，自动生成UUID
	if data.ImageID == "" {
		data.ImageID = uuid.New().String()
	}

	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: tx.Error.Error()}
	}
	return data.ID, nil
}

// InsertInTx 在事务中插入镜像记录
func (bus ObjImageBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjImage) (int64, error) {
	// 如果image_id为空，自动生成UUID
	if data.ImageID == "" {
		data.ImageID = uuid.New().String()
	}

	result := tx.Create(data)
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: result.Error.Error()}
	}
	return data.ID, nil
}

// SelectByPrimaryKey 根据主键查询镜像
func (bus ObjImageBusiness) SelectByPrimaryKey(ctx context.Context, id int64) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("id = ? AND is_delete = ?", id, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.ImageNotFound, Msg: fmt.Sprintf("镜像id [%d] 不存在", id)}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &image, nil
}

// SelectByImageID 根据image_id查询镜像
func (bus ObjImageBusiness) SelectByImageID(ctx context.Context, imageID string) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("image_id = ? AND is_delete = ?", imageID, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.ImageNotFound, Msg: fmt.Sprintf("镜像image_id [%s] 不存在", imageID)}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &image, nil
}

// SelectByImagePath 根据镜像路径查询镜像
func (bus ObjImageBusiness) SelectByImagePath(ctx context.Context, imagePath string) (*ObjImage, error) {
	var image ObjImage
	tx := dao.GetDb(ctx).
		Where("image_path = ? AND is_delete = ?", imagePath, false).
		First(&image)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.ImageNotFound, Msg: "镜像不存在"}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &image, nil
}

// GetMapByPrimaryKeys 根据主键列表批量查询镜像，返回map
func (bus ObjImageBusiness) GetMapByPrimaryKeys(ctx context.Context, ids []int64) (map[int64]*ObjImage, error) {
	var images []*ObjImage
	tx := dao.GetDb(ctx).
		Where("id IN ? AND is_delete = ?", ids, false).
		Find(&images)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}

	result := make(map[int64]*ObjImage)
	for _, image := range images {
		result[image.ID] = image
	}
	return result, nil
}

// Update 更新镜像记录
func (bus ObjImageBusiness) Update(ctx context.Context, data *ObjImage) error {
	tx := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", data.ID, false).
		Updates(map[string]any{
			"image_id":            data.ImageID,
			"image_path":          data.ImagePath,
			"image_description":   data.ImageDescription,
			"container_port":      data.ContainerPort,
			"container_env":       data.ContainerEnv,
			"container_command":   data.ContainerCommand,
			"container_args":      data.ContainerArgs,
			"container_mounts":    data.ContainerMounts,
			"container_resources": data.ContainerResources,
			"root_user":           data.RootUser,
		})
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: tx.Error.Error()}
	}
	return nil
}

// UpdateInTx 在事务中更新镜像记录
func (bus ObjImageBusiness) UpdateInTx(ctx context.Context, tx *gorm.DB, data *ObjImage) error {
	result := tx.Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", data.ID, false).
		Updates(map[string]any{
			"image_id":            data.ImageID,
			"image_path":          data.ImagePath,
			"image_description":   data.ImageDescription,
			"container_port":      data.ContainerPort,
			"container_env":       data.ContainerEnv,
			"container_command":   data.ContainerCommand,
			"container_args":      data.ContainerArgs,
			"container_mounts":    data.ContainerMounts,
			"container_resources": data.ContainerResources,
		})
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: result.Error.Error()}
	}
	return nil
}

// Delete 软删除镜像记录
func (bus ObjImageBusiness) Delete(ctx context.Context, id int64) error {
	tx := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", id, false).
		Update("is_delete", true)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLDeleteError, Msg: tx.Error.Error()}
	}
	return nil
}

// DeleteInTx 在事务中软删除镜像记录
func (bus ObjImageBusiness) DeleteInTx(ctx context.Context, tx *gorm.DB, id int64) error {
	result := tx.Model(&ObjImage{}).
		Where("id = ? AND is_delete = ?", id, false).
		Update("is_delete", true)
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLDeleteError, Msg: result.Error.Error()}
	}
	return nil
}

// SelectByPage 分页查询镜像
func (bus ObjImageBusiness) SelectByPage(ctx context.Context, page, size int64) (int64, []*ObjImage, error) {
	query := dao.GetDb(ctx).Model(&ObjImage{}).
		Where("is_delete = ?", false)

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	// 分页查询
	var images []*ObjImage
	offset := (page - 1) * size
	err = query.Offset(int(offset)).Limit(int(size)).Find(&images).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	return total, images, nil
}
