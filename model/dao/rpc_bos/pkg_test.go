package dao_test

import (
	"context"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	mcp_env_dao "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
}

func tearDownAll() {}

func TestUploadFile(t *testing.T) {
	setUpAll()
	defer tearDownAll()
	pathPrefix := "/Users/<USER>/Downloads"
	bosPrefix := "tmp"
	// fileNames := []string{"guesthouse_search.py","review_management.py","guesthouse_booking.py"}
	// fileNames := []string{"AirlineBooking.py", "ExcelOperationsServer.py", "PDFOperationsServer.py", "WordDocumentOperationsServer.py"}
	fileNames := []string{"github-mcp-server"}
	for _, fileName := range fileNames {
		file, err := os.Open(pathPrefix + "/" + fileName)
		if err != nil {
			t.Fatalf("打开文件失败: %v", err)
		}
		defer file.Close()
		err = rpc_bos.UploadObjectFromStream(ctx, bosPrefix+"/"+fileName, file)
		if err != nil {
			t.Fatalf("上传测试文件失败: %v", err)
		}

		// 获取永久链接
		permanentURL := rpc_bos.GenerateVisitURL(ctx, bosPrefix+"/"+fileName, -1)
		t.Logf("fileName: %s, permanentURL: %s", fileName, permanentURL)
	}
}

func TestBosBasicOperations(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestUploadAndDownloadString", func(t *testing.T) {
		key := "mcp_online_server/test/string_upload.txt"
		testData := "Hello World from BOS Test"

		// 测试字符串上传（注意：这里可能会失败，因为需要真实的BOS配置）
		err := rpc_bos.UploadObjectFromString(ctx, key, testData, "")
		if err != nil {
			t.Logf("字符串上传测试失败（预期，因为可能没有真实的BOS配置）: %v", err)
			return
		}

		// 测试下载
		lines, err := rpc_bos.GetObjectByLines(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, testData, strings.Join(lines, "\n"))

		// 清理
		err = rpc_bos.DeleteObject(ctx, key)
		assert.NoError(t, err)
	})

	t.Run("TestUploadStringList", func(t *testing.T) {
		key := "mcp_online_server/test/string_list_upload.txt"
		testLines := []string{"Line 1", "Line 2", "Line 3"}

		// 测试字符串列表上传
		err := rpc_bos.UploadObjectFromStringList(ctx, key, testLines)
		if err != nil {
			t.Logf("字符串列表上传测试失败（预期，因为可能没有真实的BOS配置）: %v", err)
			return
		}

		// 测试下载验证
		downloadedLines, err := rpc_bos.GetObjectByLines(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, testLines, downloadedLines[:len(testLines)]) // 去掉可能的空行

		// 清理
		err = rpc_bos.DeleteObject(ctx, key)
		assert.NoError(t, err)
	})

	t.Run("TestGenerateVisitURL", func(t *testing.T) {
		objectName := "mcp_online_server/test/sample_object.txt"
		expireInSeconds := 3600

		// 测试生成访问URL
		url := rpc_bos.GenerateVisitURL(ctx, objectName, expireInSeconds)
		assert.NotEmpty(t, url)
		assert.Contains(t, url, objectName)
		t.Logf("生成的BOS访问URL: %s", url)
	})
}

func TestBosBusinessOperations(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestPackageDirectoryToBos", func(t *testing.T) {
		// 创建临时测试目录
		tempDir, err := os.MkdirTemp("", "bos_test_env_*")
		assert.NoError(t, err)
		defer os.RemoveAll(tempDir)

		// 在临时目录中创建一些测试文件
		testFiles := map[string]string{
			"app.py":           "print('Hello World')",
			"requirements.txt": "requests==2.28.0\nflask==2.2.0",
			"config/app.json":  `{"debug": true, "port": 8080}`,
			"data/sample.txt":  "Sample data file content",
		}

		for filePath, content := range testFiles {
			fullPath := filepath.Join(tempDir, filePath)
			err := os.MkdirAll(filepath.Dir(fullPath), 0755)
			assert.NoError(t, err)
			err = os.WriteFile(fullPath, []byte(content), 0644)
			assert.NoError(t, err)
		}

		// 准备环境依赖配置
		envDependency := base.JSONData{
			"python_version": "3.9",
			"packages":       []string{"requests", "flask"},
			"entry_point":    "app.py",
		}

		// 测试目录打包上传
		result, err := rpc_bos.PackageDirectoryToBos(
			ctx,
			tempDir,
			"test_python_env",
			"Python测试环境",
			envDependency,
		)

		if err != nil {
			t.Logf("目录打包上传测试失败（预期，因为可能没有真实的BOS配置）: %v", err)
			return
		}

		// 验证结果
		assert.NotEmpty(t, result.MD5)
		assert.NotEmpty(t, result.BosURL)
		assert.Greater(t, result.FileSize, int64(0))
		assert.NotEmpty(t, result.BosKey)

		t.Logf("目录打包结果: MD5=%s, Size=%d, URL=%s", result.MD5, result.FileSize, result.BosURL)

		// 测试MD5去重功能 - 再次打包同样的目录
		result2, err := rpc_bos.PackageDirectoryToBos(
			ctx,
			tempDir,
			"test_python_env_duplicate",
			"重复的Python环境",
			envDependency,
		)
		assert.NoError(t, err)

		// 验证MD5相同，且使用了已存在的记录
		assert.Equal(t, result.MD5, result2.MD5)
		assert.True(t, result2.IsExisting)
		assert.Equal(t, result.EnvID, result2.EnvID)

		t.Logf("MD5去重测试通过: 第二次打包复用了现有记录")
	})

	t.Run("TestCheckEnvExistsByMD5", func(t *testing.T) {
		// 创建一个测试环境记录
		envDependency := base.JSONData{
			"language": "go",
			"version":  "1.21",
		}

		testEnv := &mcp_env_dao.ObjMcpEnv{
			EnvMd5:        "test_md5_12345678901234567890",
			Name:          "test_go_env",
			Description:   "Go测试环境",
			BosURL:        "https://test-bucket.example.com/go_env.tar.gz",
			EnvDependency: envDependency,
		}

		envID, err := mcp_env_dao.McpEnvBusinessIns.Insert(ctx, testEnv)
		if err != nil {
			t.Logf("插入测试环境失败（可能没有数据库配置）: %v", err)
			return
		}

		// 测试检查环境是否存在
		exists, env, err := rpc_bos.CheckEnvExistsByMD5(ctx, testEnv.EnvMd5)
		assert.NoError(t, err)
		assert.True(t, exists)
		assert.NotNil(t, env)
		assert.Equal(t, testEnv.EnvMd5, env.EnvMd5)
		assert.Equal(t, testEnv.Name, env.Name)

		// 测试检查不存在的环境
		exists2, env2, err2 := rpc_bos.CheckEnvExistsByMD5(ctx, "non_existent_md5")
		assert.NoError(t, err2)
		assert.False(t, exists2)
		assert.Nil(t, env2)

		// 清理测试数据
		err = mcp_env_dao.McpEnvBusinessIns.Delete(ctx, envID)
		assert.NoError(t, err)
	})

	t.Run("TestDownloadEnvFromBos", func(t *testing.T) {
		// 这个测试需要真实的BOS文件，在没有真实环境的情况下会失败
		envMd5 := "test_download_md5_987654321"
		destDir, err := os.MkdirTemp("", "bos_download_test_*")
		assert.NoError(t, err)
		defer os.RemoveAll(destDir)

		// 创建一个测试环境记录
		testEnv := &mcp_env_dao.ObjMcpEnv{
			EnvMd5:      envMd5,
			Name:        "test_download_env_2",
			Description: "BOS下载测试环境",
			BosURL:      "https://test-bucket.example.com/mcp_env/test_file.tar.gz",
		}

		envID, err := mcp_env_dao.McpEnvBusinessIns.Insert(ctx, testEnv)
		if err != nil {
			t.Logf("插入测试环境失败（可能没有数据库配置）: %v", err)
			return
		}

		// 测试下载和解压
		err = rpc_bos.DownloadEnvFromBos(ctx, envMd5, destDir)
		if err != nil {
			t.Logf("下载环境失败（预期，因为可能没有真实的BOS文件）: %v", err)
		} else {
			// 验证解压后的文件
			files, err := os.ReadDir(destDir)
			assert.NoError(t, err)
			assert.Greater(t, len(files), 0)
			t.Logf("下载解压成功，文件数量: %d", len(files))
		}

		// 清理测试数据
		err = mcp_env_dao.McpEnvBusinessIns.Delete(ctx, envID)
		assert.NoError(t, err)
	})
}

func TestBosFileOperations(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestGetFileSizeOperations", func(t *testing.T) {
		key := "mcp_online_server/test/size_test_file.txt"
		testData := "This is a test file for size calculation operations"

		// 上传测试文件
		err := rpc_bos.UploadObjectFromString(ctx, key, testData, "")
		if err != nil {
			t.Logf("上传测试文件失败（预期，因为可能没有真实的BOS配置）: %v", err)
			return
		}

		// 测试获取文件大小
		fileSize, err := rpc_bos.GetFileSize(key)
		assert.NoError(t, err)
		assert.Equal(t, int64(len(testData)), fileSize)
		// 清理
		rpc_bos.DeleteObject(ctx, key)
	})
}

func TestBosErrorHandling(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestNonExistentOperations", func(t *testing.T) {
		key := "mcp_online_server/test/non-existent-key"

		// 测试下载不存在的文件，会返回错误
		_, err := rpc_bos.GetObject(ctx, key)
		assert.Error(t, err)

		// 测试获取不存在文件的大小
		_, err = rpc_bos.GetFileSize(key)
		assert.Error(t, err)

		// 测试删除不存在的文件
		err = rpc_bos.DeleteObject(ctx, key)
		assert.Error(t, err)
	})

	t.Run("TestInvalidDirectoryPackaging", func(t *testing.T) {
		// 测试打包不存在的目录
		nonExistentDir := "/path/to/non/existent/directory"
		_, err := rpc_bos.PackageDirectoryToBos(ctx, nonExistentDir, "test", "test", nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "目录不存在")
	})

}
