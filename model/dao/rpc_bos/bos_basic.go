package dao

import (
	"context"
	"fmt"
	"io"
	"strings"

	bos_api "github.com/baidubce/bce-sdk-go/services/bos/api"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
)

// UploadObjectFromStream 从流上传文件到BOS
//
// 参数：
//   - key: 对象键
//   - stream: 要上传的文件流
func UploadObjectFromStream(ctx context.Context, key string, stream io.Reader) error {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	resource.LoggerService.Notice(ctx, fmt.Sprintf("UploadObjectFromStream to bucket[%s] key[%s]", bucket, key))
	_, err := resource.StorageBosClient.PutObjectFromStream(bucket, key, stream, nil)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.BosUploadObjectError, Msg: "bos 上传文件失败", ExtData: err.Error()}
	}
	return nil
}

// UploadObjectFromString 将字符串数据上传到BOS
//
// 参数：
//   - key: 对象键
//   - data: 要上传的字符串数据
func UploadObjectFromString(ctx context.Context, key string, data string, contentType string) error {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	// 构建上传参数
	args := &bos_api.PutObjectArgs{}

	// 设置Content-Type
	if contentType == "" {
		// 根据文件扩展名自动判断Content-Type
		if strings.HasSuffix(key, ".log") {
			// 对于日志文件，设置为UTF-8编码的文本类型
			args.ContentType = "text/plain; charset=utf-8"
		} else if strings.HasSuffix(key, ".json") {
			args.ContentType = "application/json; charset=utf-8"
		} else if strings.HasSuffix(key, ".txt") {
			args.ContentType = "text/plain; charset=utf-8"
		} else if strings.HasSuffix(key, ".xml") {
			args.ContentType = "application/xml; charset=utf-8"
		} else if strings.HasSuffix(key, ".html") {
			args.ContentType = "text/html; charset=utf-8"
		} else {
			// 默认为二进制流
			args.ContentType = "application/octet-stream"
		}
	} else {
		args.ContentType = contentType
	}

	_, err := resource.StorageBosClient.PutObjectFromString(bucket, key, data, args)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.BosUploadObjectError, Msg: "bos 上传文件失败", ExtData: err.Error()}
	}
	return nil
}

// UploadObjectFromStringList 将字符串列表上传到BOS
//
// 参数：
//   - key: 对象键
//   - lines: 要上传的字符串列表
func UploadObjectFromStringList(ctx context.Context, key string, lines []string) error {
	data := strings.Join(lines, "\n") // 将字符串列表连接为单个字符串
	return UploadObjectFromString(ctx, key, data, "")
}

// GenerateVisitURL 生成BOS对象的访问URL
//
// 参数：
//   - bucket: 存储桶名称
//   - objectName: 对象名称
//   - expireInSeconds: 过期时间（秒）
func GenerateVisitURL(ctx context.Context, objectName string, expireInSeconds int) string {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	generatedURL := resource.StorageBosClient.BasicGeneratePresignedUrl(bucket, objectName, expireInSeconds)
	resource.LoggerService.Notice(ctx, fmt.Sprintf("bos 获取访问 url 成功, objectName: %s, url: %s", objectName, generatedURL))
	return generatedURL
}

// GetObject 从BOS获取对象流
//
// 参数：
//   - bucket: 存储桶名称
//   - key: 对象键
func GetObject(ctx context.Context, key string) (io.ReadCloser, error) {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	object, err := resource.StorageBosClient.BasicGetObject(bucket, key)
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.BosGetObjectError, Msg: "拉取文件失败", ExtData: err.Error()}
	}
	return object.Body, nil
}

// GetObjectByLines 从BOS获取对象并按行返回
//
// 参数：
//   - key: 对象键
func GetObjectByLines(ctx context.Context, key string) ([]string, error) {
	object, err := GetObject(ctx, key)
	if err != nil {
		return nil, err
	}
	defer object.Close()

	objectBytes, err := io.ReadAll(object)
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.BosGetObjectError, Msg: "bos 读取文件失败", ExtData: err.Error()}
	}
	return strings.Split(string(objectBytes), "\n"), nil
}

// GetObjectToString 从BOS获取对象并返回字符串内容
//
// 参数：
//   - key: 对象键
func GetObjectToString(ctx context.Context, key string) (string, error) {
	object, err := GetObject(ctx, key)
	if err != nil {
		return "", err
	}
	defer object.Close()

	objectBytes, err := io.ReadAll(object)
	if err != nil {
		return "", &lib_error.CustomErr{Code: errcode.BosGetObjectError, Msg: "bos 读取文件失败", ExtData: err.Error()}
	}
	return string(objectBytes), nil
}

// GetObjectToFile 将BOS对象下载到本地文件
//
// 参数：
//   - bucket: 存储桶名称
//   - key: 对象键
//   - filePath: 本地文件路径
func GetObjectToFile(ctx context.Context, key, filePath string) error {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	resource.LoggerService.Notice(ctx, fmt.Sprintf("GetObjectToFile from bucket[%s] key[%s] to file[%s]", bucket, key, filePath))
	return resource.StorageBosClient.BasicGetObjectToFile(bucket, key, filePath)
}

// GetObjectMeta 获取BOS对象的元数据
//
// 参数：
//   - bosKey: 对象键
func GetObjectMeta(bosKey string) (*bos_api.GetObjectMetaResult, error) {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	meta, err := resource.StorageBosClient.GetObjectMeta(bucket, bosKey)
	if err != nil {
		return nil, &lib_error.CustomErr{Code: errcode.BosGetFileMetaError, Msg: "获取文件 BosMeta 失败", ExtData: err.Error()}
	}
	return meta, nil
}

// GetFileSize 获取文件的大小
//
// 参数：
//   - bosKey: 对象键
func GetFileSize(bosKey string) (int64, error) {
	meta, err := GetObjectMeta(bosKey)
	if err != nil {
		return 0, err
	}
	return meta.ContentLength, nil
}

// DeleteObject 删除BOS对象
//
// 参数：
//   - bucket: 存储桶名称
//   - key: 对象键
func DeleteObject(ctx context.Context, key string) error {
	bucket := config.DataStorageBosConfigGlobal.Bucket
	resource.LoggerService.Notice(ctx, fmt.Sprintf("DeleteObject from bucket[%s] key[%s]", bucket, key))
	err := resource.StorageBosClient.DeleteObject(bucket, key)
	if err != nil {
		return &lib_error.CustomErr{Code: errcode.BosDeleteObjectError, Msg: "bos 删除文件失败", ExtData: err.Error()}
	}
	return nil
}
