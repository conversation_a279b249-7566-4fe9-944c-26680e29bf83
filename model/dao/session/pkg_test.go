package dao_test

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll() {}

func setUp() {
	dao.TxMysqlMain = dao.CliMysqlMain.Begin()
}

func tearDown() {
	dao.TxMysqlMain.Rollback()
}

func TestSessionLifecycle(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestCRUD", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		serverIds := dao.JSONArray[int64]{1, 2, 3}
		mcpTools := dao.JSONData{
			"filesystem": map[string]interface{}{
				"name":        "filesystem",
				"description": "文件系统操作工具",
			},
		}

		session := &dao_session.ObjSession{
			JobID:           "job_test_session_001",
			SessionCode:     fmt.Sprintf("test_session_crud_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ServerIds:       serverIds,
			McpTools:        mcpTools,
			ContainerStatus: dao_session.ContainerStatusPending,
		}

		// 测试插入
		id, err := dao_session.SessionBusinessIns.Insert(ctx, session)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 测试查询
		result, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, session.JobID, result.JobID)
		assert.Equal(t, session.EnvID, result.EnvID)

		// 测试更新状态
		err = dao_session.SessionBusinessIns.UpdateMap(ctx, id, map[string]any{
			"container_status": dao_session.ContainerStatusRunning,
		})
		assert.NoError(t, err)

		// 测试删除
		err = dao_session.SessionBusinessIns.Delete(ctx, id)
		assert.NoError(t, err)

		// 验证删除
		_, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
	})

	t.Run("TestInsertInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		session := &dao_session.ObjSession{
			JobID:           "job_test_session_tx_001",
			SessionCode:     fmt.Sprintf("test_session_tx_%d", time.Now().UnixNano()),
			EnvID:           2001,
			ContainerStatus: dao_session.ContainerStatusPending,
		}

		tx := dao.GetDb(ctx)
		id, err := dao_session.SessionBusinessIns.InsertInTx(ctx, tx, session)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))
	})
}

func TestSessionQueries(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestGetMapByPrimaryKeys", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试会话
		var sessionIDs []int64
		for i := 0; i < 3; i++ {
			session := &dao_session.ObjSession{
				JobID:           fmt.Sprintf("job_map_test_%d", i),
				SessionCode:     fmt.Sprintf("test_session_map_%d_%d", i, time.Now().UnixNano()),
				EnvID:           1001,
				ContainerStatus: dao_session.ContainerStatusRunning,
			}
			id, err := dao_session.SessionBusinessIns.Insert(ctx, session)
			assert.NoError(t, err)
			sessionIDs = append(sessionIDs, id)
		}

		// 测试GetMapByPrimaryKeys
		sessionsMap, err := dao_session.SessionBusinessIns.GetMapByPrimaryKeys(ctx, sessionIDs)
		assert.NoError(t, err)
		assert.Equal(t, 3, len(sessionsMap))

		// 验证所有会话都在映射中
		for _, id := range sessionIDs {
			session, exists := sessionsMap[id]
			assert.True(t, exists)
			assert.NotNil(t, session)
			assert.Equal(t, id, session.SessionID)
		}

		// 测试空切片
		emptyMap, err := dao_session.SessionBusinessIns.GetMapByPrimaryKeys(ctx, []int64{})
		assert.NoError(t, err)
		assert.Equal(t, 0, len(emptyMap))
	})

	t.Run("TestUpdate", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试会话
		serverIds := dao.JSONArray[int64]{1, 2}
		mcpTools := dao.JSONData{
			"filesystem": map[string]interface{}{
				"name":        "filesystem",
				"description": "文件系统操作工具",
			},
		}

		session := &dao_session.ObjSession{
			JobID:           "job_update_test",
			SessionCode:     fmt.Sprintf("test_session_update_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ServerIds:       serverIds,
			McpTools:        mcpTools,
			ContainerStatus: dao_session.ContainerStatusPending,
		}

		id, err := dao_session.SessionBusinessIns.Insert(ctx, session)
		assert.NoError(t, err)

		// 更新会话数据
		newServerIds := dao.JSONArray[int64]{1, 2, 3, 4}
		newMcpTools := dao.JSONData{
			"filesystem": map[string]interface{}{
				"name":        "filesystem",
				"description": "文件系统操作工具",
			},
			"database": map[string]interface{}{
				"name":        "database",
				"description": "数据库操作工具",
			},
		}
		// 执行更新
		err = dao_session.SessionBusinessIns.UpdateMap(ctx, id, map[string]any{
			"job_id":           "job_update_test_modified",
			"env_id":           2001,
			"server_ids":       newServerIds,
			"mcp_tools":        newMcpTools,
			"container_status": dao_session.ContainerStatusStopped,
			"stopped_at":       time.Now(),
		})
		assert.NoError(t, err)

		// 验证更新结果
		updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, "job_update_test_modified", updatedSession.JobID)
		assert.Equal(t, int64(2001), updatedSession.EnvID)
		assert.Equal(t, dao_session.ContainerStatusStopped, updatedSession.ContainerStatus)
		assert.NotNil(t, updatedSession.StoppedAt)
		assert.Equal(t, 4, len(updatedSession.ServerIds))
		assert.Equal(t, 2, len(updatedSession.McpTools))
	})

	t.Run("TestSelectTimeoutSessions", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建一个运行中的会话（模拟超时）
		timeoutSession := &dao_session.ObjSession{
			JobID:           "job_timeout_test",
			SessionCode:     fmt.Sprintf("test_session_timeout_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ContainerStatus: dao_session.ContainerStatusRunning,
		}

		id, err := dao_session.SessionBusinessIns.Insert(ctx, timeoutSession)
		assert.NoError(t, err)

		// 手动设置created_at为一个较早的时间（模拟超时场景）
		pastTime := time.Now().Add(-2 * time.Hour) // 2小时前

		// 直接更新数据库中的created_at字段
		tx := dao.GetDb(ctx).Model(&dao_session.ObjSession{}).
			Where("session_id = ?", id).
			Update("created_at", pastTime)
		assert.NoError(t, tx.Error)

		// 创建一个正常运行的会话（不超时）
		normalSession := &dao_session.ObjSession{
			JobID:           "job_normal_test",
			SessionCode:     fmt.Sprintf("test_session_normal_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ContainerStatus: dao_session.ContainerStatusRunning,
		}
		_, err = dao_session.SessionBusinessIns.Insert(ctx, normalSession)
		assert.NoError(t, err)

		// 创建一个Pending状态的超时会话
		pendingTimeoutSession := &dao_session.ObjSession{
			JobID:           "job_pending_timeout_test",
			SessionCode:     fmt.Sprintf("test_session_pending_timeout_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ContainerStatus: dao_session.ContainerStatusPending,
		}
		pendingId, err := dao_session.SessionBusinessIns.Insert(ctx, pendingTimeoutSession)
		assert.NoError(t, err)

		// 设置Pending会话的创建时间为较早
		tx = dao.GetDb(ctx).Model(&dao_session.ObjSession{}).
			Where("session_id = ?", pendingId).
			Update("created_at", pastTime)
		assert.NoError(t, tx.Error)

		// 查询超时会话（超时时间为1小时）
		timeoutDuration := 1 * time.Hour
		timeoutSessions, err := dao_session.SessionBusinessIns.SelectTimeoutSessions(ctx, timeoutDuration)
		assert.NoError(t, err)

		// 验证结果包含超时会话
		foundRunning := false
		foundPending := false
		for _, session := range timeoutSessions {
			if session.SessionID == id {
				foundRunning = true
				assert.Equal(t, dao_session.ContainerStatusRunning, session.ContainerStatus)
				assert.True(t, session.CreatedAt.Before(time.Now().Add(-timeoutDuration)))
			}
			if session.SessionID == pendingId {
				foundPending = true
				assert.Equal(t, dao_session.ContainerStatusPending, session.ContainerStatus)
				assert.True(t, session.CreatedAt.Before(time.Now().Add(-timeoutDuration)))
			}
		}
		assert.True(t, foundRunning, "应该找到运行中的超时会话")
		assert.True(t, foundPending, "应该找到待处理的超时会话")
	})

	t.Run("TestSelectByStatus", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建不同状态的会话
		for i := 0; i < 3; i++ {
			session := &dao_session.ObjSession{
				JobID:           fmt.Sprintf("job_status_test_%d", i),
				SessionCode:     fmt.Sprintf("test_session_status_%d_%d", i, time.Now().UnixNano()),
				EnvID:           1001,
				ContainerStatus: dao_session.ContainerStatusRunning,
			}
			_, err := dao_session.SessionBusinessIns.Insert(ctx, session)
			assert.NoError(t, err)
		}

		// 测试按状态查询
		sessions, err := dao_session.SessionBusinessIns.SelectByStatus(ctx, dao_session.ContainerStatusRunning)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(sessions), 3)
	})

	t.Run("TestSelectActiveSessions", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建活跃会话
		for i := 0; i < 2; i++ {
			session := &dao_session.ObjSession{
				JobID:           fmt.Sprintf("job_active_test_%d", i),
				SessionCode:     fmt.Sprintf("test_session_active_%d_%d", i, time.Now().UnixNano()),
				EnvID:           1001,
				ContainerStatus: dao_session.ContainerStatusRunning,
			}
			_, err := dao_session.SessionBusinessIns.Insert(ctx, session)
			assert.NoError(t, err)
		}

		// 测试查询活跃会话
		activeSessions, err := dao_session.SessionBusinessIns.SelectActiveSessions(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(activeSessions), 2)
	})

	t.Run("TestSelectByPage", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试会话
		for i := 0; i < 5; i++ {
			session := &dao_session.ObjSession{
				JobID:           fmt.Sprintf("job_page_test_%d", i),
				SessionCode:     fmt.Sprintf("test_session_page_%d_%d", i, time.Now().UnixNano()),
				EnvID:           1001,
				ContainerStatus: dao_session.ContainerStatusPending,
			}
			_, err := dao_session.SessionBusinessIns.Insert(ctx, session)
			assert.NoError(t, err)
		}

		// 测试分页查询
		total, sessions, err := dao_session.SessionBusinessIns.SelectByPage(ctx, 1, 10, "")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
		assert.GreaterOrEqual(t, len(sessions), 5)
	})
}

func TestSessionErrorHandling(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestUpdateStatusWithError", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试会话
		session := &dao_session.ObjSession{
			JobID:           "job_error_test",
			SessionCode:     fmt.Sprintf("test_session_error_%d", time.Now().UnixNano()),
			EnvID:           1001,
			ContainerStatus: dao_session.ContainerStatusInit,
		}

		id, err := dao_session.SessionBusinessIns.Insert(ctx, session)
		assert.NoError(t, err)

		// 测试更新状态并记录错误信息
		errorMsg := "Container creation failed: insufficient resources"
		err = dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, id, dao_session.ContainerStatusFailed, errorMsg)
		assert.NoError(t, err)

		// 验证更新结果
		result, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, dao_session.ContainerStatusFailed, result.ContainerStatus)
		assert.NotNil(t, result.ErrMsg)
		assert.Equal(t, errorMsg, *result.ErrMsg)
		assert.NotNil(t, result.StoppedAt)
	})

}
