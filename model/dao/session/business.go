package dao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

var SessionBusinessIns = SessionBusiness{}

type SessionBusiness struct{}

// Insert 插入会话记录
func (bus SessionBusiness) Insert(ctx context.Context, data *ObjSession) (int64, error) {
	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: tx.Error.Error()}
	}

	// Record session creation metrics
	metrics_helper.RecordSessionCreated()

	return data.SessionID, nil
}

// InsertInTx 在事务中插入会话记录
func (bus SessionBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjSession) (int64, error) {
	result := tx.Create(data)
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: result.Error.Error()}
	}
	return data.SessionID, nil
}

// SelectByPrimaryKey 根据主键查询会话
func (bus SessionBusiness) SelectByPrimaryKey(ctx context.Context, sessionID int64) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).
		Where("session_id = ? AND is_delete = ?", sessionID, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.SessionNotFound, Msg: "Session不存在"}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &session, nil
}

// SelectByJobID 根据作业ID查询会话
func (bus SessionBusiness) SelectByJobID(ctx context.Context, jobID string) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("job_id = ? AND is_delete = ?", jobID, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &session, nil
}

// GetSessionByCode 根据会话代码查询会话
func (bus SessionBusiness) GetSessionByCode(ctx context.Context, sessionCode string) (*ObjSession, error) {
	var session ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_code = ? AND is_delete = ?", sessionCode, false).
		First(&session)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.SessionNotFound, Msg: "Session不存在"}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &session, nil
}

// GetMapByPrimaryKeys 根据主键列表获取会话映射
func (bus SessionBusiness) GetMapByPrimaryKeys(ctx context.Context, sessionIDs []int64) (map[int64]*ObjSession, error) {
	if len(sessionIDs) == 0 {
		return make(map[int64]*ObjSession), nil
	}

	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_id IN (?) AND is_delete = ?", sessionIDs, false).
		Find(&sessions)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}

	sessionsMap := make(map[int64]*ObjSession)
	for _, item := range sessions {
		sessionsMap[item.SessionID] = item
	}
	return sessionsMap, nil
}

// UpdateStatus 更新会话状态
func (bus SessionBusiness) UpdateJobIDAndStatus(ctx context.Context, sessionID int64, jobID string, status ContainerStatus) error {
	updates := map[string]any{
		"job_id":           jobID,
		"container_status": status,
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}

func (bus SessionBusiness) UpdateMcpToolsAndStatus(ctx context.Context, sessionID int64, mcpToolsJSON *dao.JSONData, status ContainerStatus) error {
	updates := map[string]any{
		"mcp_tools":        mcpToolsJSON,
		"container_status": status,
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}

// UpdateStatusWithError 更新会话状态并记录错误信息
func (bus SessionBusiness) UpdateStatusWithError(ctx context.Context, sessionID int64, status ContainerStatus, errMsg string) error {

	updates := map[string]any{
		"container_status": status,
	}
	if errMsg != "" {
		// 截断过长的错误信息，保留前15000个字符
		const maxErrMsgLength = 15000
		if len(errMsg) > maxErrMsgLength {
			errMsg = errMsg[:maxErrMsgLength] + "...(truncated)"
		}
		updates["err_msg"] = &errMsg
	}
	fillStopAt(updates, status)

	err := bus.UpdateMap(ctx, sessionID, updates)
	if err == nil {
		// Record status change metrics
		switch status {
		case ContainerStatusPending:
			metrics_helper.RecordSessionPending()
		case ContainerStatusRunning:
			metrics_helper.RecordSessionRunning()
		case ContainerStatusStopping:
			metrics_helper.RecordSessionStopping()
		case ContainerStatusStopped:
			metrics_helper.RecordSessionStopped()
		case ContainerStatusTimeout:
			metrics_helper.RecordSessionTimeout()
		case ContainerStatusFailed:
			metrics_helper.RecordSessionFailed()
		}
	}

	return err
}
func (bus SessionBusiness) UpdateMap(ctx context.Context, sessionID int64, updates map[string]any) error {
	tx := dao.GetDb(ctx).Model(&ObjSession{}).Where("session_id = ? AND is_delete = ?", sessionID, false).Updates(updates)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: tx.Error.Error()}
	}
	return nil
}

// Delete 软删除会话记录
func (bus SessionBusiness) Delete(ctx context.Context, sessionID int64) error {
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("session_id = ? AND is_delete = ?", sessionID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: tx.Error.Error()}
	}
	return nil
}

// SelectByStatus 根据状态查询会话列表
func (bus SessionBusiness) SelectByStatus(ctx context.Context, status ContainerStatus) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status = ? AND is_delete = ?", status, false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return sessions, nil
}

// SelectByEnvID 根据环境ID查询会话列表
func (bus SessionBusiness) SelectByEnvID(ctx context.Context, envID int64) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("env_id = ? AND is_delete = ?", envID, false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return sessions, nil
}

// SessionLogRecord 记录会话日志到BOS
func (bus SessionBusiness) SessionLogRecord(ctx context.Context, sessionID int64, jobID string) error {
	// 2. 检查会话是否有关联的Kubernetes任务
	if jobID == "" {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("会话[%d]没有关联的Kubernetes任务", sessionID))
		return &lib_error.CustomErr{Code: errcode.K8sContainerStartError, Msg: "会话没有关联的Kubernetes任务"}
	}
	// 3. 从Kubernetes获取任务日志
	logResp, err := rpc_k8s_proxy.K8sProxyClientIns.GetTaskLogStream(ctx, jobID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("获取Kubernetes任务日志失败: %v", err))
		return &lib_error.CustomErr{Code: errcode.K8sContainerStartError, Msg: fmt.Sprintf("获取Kubernetes任务日志失败: %v", err)}
	}

	// 4. 处理日志内容 - 将\n转换为实际换行符
	logContent := strings.ReplaceAll(logResp.Log, "\\n", "\n")

	// 5. 生成BOS存储路径
	bosKey := fmt.Sprintf("mcp_log/%s.log", jobID)

	// 6. 上传日志到BOS
	err = rpc_bos.UploadObjectFromString(ctx, bosKey, logContent, "text/plain; charset=utf-8")
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("上传日志到BOS失败: %v", err))
		return &lib_error.CustomErr{Code: errcode.BosUploadObjectError, Msg: fmt.Sprintf("上传日志到BOS失败: %v", err)}
	}

	// 7. 获取BOS下载链接
	logURL := rpc_bos.GenerateVisitURL(ctx, bosKey, -1)

	// 8. 更新会话记录，存储日志下载链接
	err = bus.UpdateMap(ctx, sessionID, map[string]any{
		"log_url": &logURL,
	})
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("更新会话日志URL失败: %v", err))
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: fmt.Sprintf("更新会话日志URL失败: %v", err)}
	}
	resource.LoggerService.Notice(ctx, fmt.Sprintf("会话[%d]日志记录成功: task_id=%s, bos_key=%s, log_url=%s", sessionID, jobID, bosKey, logURL))
	return nil
}

// SelectByPage 分页查询会话
func (bus SessionBusiness) SelectByPage(ctx context.Context, page, size int64, status ContainerStatus) (int64, []*ObjSession, error) {
	query := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("is_delete = ?", false)

	if status != "" {
		query = query.Where("container_status = ?", status)
	}

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	// 分页查询
	offset := (page - 1) * size
	var sessions []*ObjSession
	err = query.Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(size)).
		Find(&sessions).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	return total, sessions, nil
}

// SelectActiveSessions 查询活跃会话（运行中的会话）
func (bus SessionBusiness) SelectActiveSessions(ctx context.Context) ([]*ObjSession, error) {
	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status IN (?) AND is_delete = ?",
			[]ContainerStatus{ContainerStatusPending, ContainerStatusRunning},
			false).
		Order("created_at DESC").
		Find(&sessions)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return sessions, nil
}

// SelectTimeoutSessions 查询超时会话（运行超过指定时间的会话）
func (bus SessionBusiness) SelectTimeoutSessions(ctx context.Context, timeoutDuration time.Duration) ([]*ObjSession, error) {
	timeoutBefore := time.Now().Add(-timeoutDuration)

	var sessions []*ObjSession
	tx := dao.GetDb(ctx).Model(&ObjSession{}).
		Where("container_status IN (?) AND created_at < ? AND is_delete = ?",
			[]ContainerStatus{ContainerStatusPending, ContainerStatusRunning},
			timeoutBefore,
			false).
		Order("created_at ASC").
		Find(&sessions)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return sessions, nil
}

func fillStopAt(updates map[string]any, status ContainerStatus) {
	if status == ContainerStatusStopping || status == ContainerStatusStopped || status == ContainerStatusTimeout || status == ContainerStatusFailed {
		now := time.Now()
		updates["stopped_at"] = &now
	}
}
