package dao

import (
	"context"

	"gorm.io/gorm"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
)

var McpEnvBusinessIns = McpEnvBusiness{}

type McpEnvBusiness struct{}

// Insert 插入MCP环境记录
func (bus McpEnvBusiness) Insert(ctx context.Context, data *ObjMcpEnv) (int64, error) {
	tx := dao.GetDb(ctx).Create(data)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: tx.Error.Error()}
	}
	return data.EnvID, nil
}

// InsertInTx 在事务中插入MCP环境记录
func (bus McpEnvBusiness) InsertInTx(ctx context.Context, tx *gorm.DB, data *ObjMcpEnv) (int64, error) {
	result := tx.Create(data)
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return 0, &lib_error.CustomErr{Code: errcode.SQLInsertError, Msg: result.Error.Error()}
	}
	return data.EnvID, nil
}

// CheckEnvMd5Exist 检查环境MD5是否存在
func (bus McpEnvBusiness) CheckEnvMd5Exist(ctx context.Context, envMd5 string, excludeEnvID int64) (bool, error) {
	var env ObjMcpEnv
	query := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("env_md5 = ? AND is_delete = ?", envMd5, false)

	if excludeEnvID > 0 {
		query = query.Where("env_id != ?", excludeEnvID)
	}

	tx := query.First(&env)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return false, nil
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return false, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return true, nil
}

// SelectByPrimaryKey 根据主键查询MCP环境
func (bus McpEnvBusiness) SelectByPrimaryKey(ctx context.Context, envID int64) (*ObjMcpEnv, error) {
	var env ObjMcpEnv
	tx := dao.GetDb(ctx).
		Where("env_id = ? AND is_delete = ?", envID, false).
		First(&env)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, &lib_error.CustomErr{Code: errcode.EnvironmentNotFound, Msg: "MCP环境不存在"}
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &env, nil
}

// SelectByEnvMd5 根据环境MD5查询MCP环境
func (bus McpEnvBusiness) SelectByEnvMd5(ctx context.Context, envMd5 string) (*ObjMcpEnv, error) {
	var env ObjMcpEnv
	tx := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("env_md5 = ? AND is_delete = ?", envMd5, false).
		First(&env)
	if tx.Error != nil {
		if tx.Error == gorm.ErrRecordNotFound {
			return nil, nil
		}
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return &env, nil
}

// GetMapByPrimaryKeys 根据主键列表获取MCP环境映射
func (bus McpEnvBusiness) GetMapByPrimaryKeys(ctx context.Context, envIDs []int64) (map[int64]*ObjMcpEnv, error) {
	if len(envIDs) == 0 {
		return make(map[int64]*ObjMcpEnv), nil
	}

	var envs []*ObjMcpEnv
	tx := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("env_id IN (?) AND is_delete = ?", envIDs, false).
		Find(&envs)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}

	envsMap := make(map[int64]*ObjMcpEnv)
	for _, item := range envs {
		envsMap[item.EnvID] = item
	}
	return envsMap, nil
}

// Update 更新MCP环境记录
func (bus McpEnvBusiness) Update(ctx context.Context, data *ObjMcpEnv) error {
	tx := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("env_id = ? AND is_delete = ?", data.EnvID, false).
		Updates(map[string]any{
			"env_md5":        data.EnvMd5,
			"name":           data.Name,
			"description":    data.Description,
			"bos_url":        data.BosURL,
			"env_dependency": data.EnvDependency,
			"file_size":      data.FileSize,
			"updated_at":     data.UpdatedAt,
		})
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: tx.Error.Error()}
	}
	return nil
}

// UpdateInTx 在事务中更新MCP环境记录
func (bus McpEnvBusiness) UpdateInTx(ctx context.Context, tx *gorm.DB, data *ObjMcpEnv) error {
	result := tx.Model(&ObjMcpEnv{}).
		Where("env_id = ? AND is_delete = ?", data.EnvID, false).
		Updates(map[string]any{
			"env_md5":        data.EnvMd5,
			"name":           data.Name,
			"description":    data.Description,
			"bos_url":        data.BosURL,
			"env_dependency": data.EnvDependency,
			"file_size":      data.FileSize,
			"updated_at":     data.UpdatedAt,
		})
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: result.Error.Error()}
	}
	return nil
}

// Delete 软删除MCP环境记录
func (bus McpEnvBusiness) Delete(ctx context.Context, envID int64) error {
	tx := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("env_id = ? AND is_delete = ?", envID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: tx.Error.Error()}
	}
	return nil
}

// DeleteInTx 在事务中软删除MCP环境记录
func (bus McpEnvBusiness) DeleteInTx(ctx context.Context, tx *gorm.DB, envID int64) error {
	result := tx.Model(&ObjMcpEnv{}).
		Where("env_id = ? AND is_delete = ?", envID, false).
		Updates(map[string]any{
			"is_delete": true,
		})
	if result.Error != nil {
		resource.LoggerService.Warning(ctx, result.Error.Error())
		return &lib_error.CustomErr{Code: errcode.SQLUpdateError, Msg: result.Error.Error()}
	}
	return nil
}

// SelectAllActive 查询所有有效的MCP环境
func (bus McpEnvBusiness) SelectAllActive(ctx context.Context) ([]*ObjMcpEnv, error) {
	var envs []*ObjMcpEnv
	tx := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("is_delete = ?", false).
		Order("created_at DESC").
		Find(&envs)
	if tx.Error != nil {
		resource.LoggerService.Warning(ctx, tx.Error.Error())
		return nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: tx.Error.Error()}
	}
	return envs, nil
}

// SelectByPage 分页查询MCP环境
func (bus McpEnvBusiness) SelectByPage(ctx context.Context, page, size int64, keyword string) (int64, []*ObjMcpEnv, error) {
	query := dao.GetDb(ctx).Model(&ObjMcpEnv{}).
		Where("is_delete = ?", false)

	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 计算总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	// 分页查询
	offset := (page - 1) * size
	var envs []*ObjMcpEnv
	err = query.Order("created_at DESC").
		Offset(int(offset)).
		Limit(int(size)).
		Find(&envs).Error
	if err != nil {
		resource.LoggerService.Warning(ctx, err.Error())
		return 0, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: err.Error()}
	}

	return total, envs, nil
}
