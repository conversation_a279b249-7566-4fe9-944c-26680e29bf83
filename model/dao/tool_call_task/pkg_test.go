package dao_test

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll() {
	bootstrapOnceSync.Do(bootstrapOnce)
	dao.OrmLazyInit()
}

func tearDownAll() {}

func setUp() {
	dao.TxMysqlMain = dao.CliMysqlMain.Begin()
}

func tearDown() {
	dao.TxMysqlMain.Rollback()
}

func TestToolCallTaskLifecycle(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestCRUD", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		arguments := dao.JSONData{
			"path":      "/home/<USER>/documents",
			"recursive": true,
			"pattern":   "*.txt",
		}
		argumentsBytes, err := json.Marshal(arguments)
		assert.NoError(t, err)
		argumentsStr := string(argumentsBytes)
		result := dao.JSONData{
			"files": []string{
				"/home/<USER>/documents/readme.txt",
				"/home/<USER>/documents/notes.txt",
			},
			"total_count": 2,
		}
		resultBytes, err := json.Marshal(result)
		assert.NoError(t, err)
		resultStr := string(resultBytes)

		task := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_test_filesystem_001",
			SessionID: int64(1),
			ToolName:  "filesystem",
			Arguments: argumentsStr,
			Result:    resultStr,
			OldEnvMd5: "old_env_md5_123",
			NewEnvMd5: "new_env_md5_456",
			Status:    dao_tool_call_task.ToolCallTaskStatusSuccess,
		}

		// 测试插入
		id, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 测试查询
		result_task, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result_task)
		assert.Equal(t, task.CallID, result_task.CallID)
		assert.Equal(t, task.SessionID, result_task.SessionID)
		assert.Equal(t, task.ToolName, result_task.ToolName)
		assert.Equal(t, task.Status, result_task.Status)

		// 测试按CallID查询
		resultByCallID, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByCallID(ctx, int64(1), task.CallID)
		assert.NoError(t, err)
		assert.NotNil(t, resultByCallID)
		assert.Equal(t, id, resultByCallID.TaskID)

		// 测试更新状态
		err = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateStatus(ctx, id, dao_tool_call_task.ToolCallTaskStatusRunning, "")
		assert.NoError(t, err)

		// 验证状态更新结果
		updatedResult, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusRunning, updatedResult.Status)
		assert.NotNil(t, updatedResult.StartedAt)

		// 测试删除
		err = dao_tool_call_task.ToolCallTaskBusinessIns.Delete(ctx, id)
		assert.NoError(t, err)

		// 验证删除
		_, err = dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
	})

	t.Run("TestInsertInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试数据
		arguments := dao.JSONData{
			"query":    "SELECT * FROM users",
			"database": "production",
		}
		argumentsBytes, err := json.Marshal(arguments)
		assert.NoError(t, err)
		argumentsStr := string(argumentsBytes)

		task := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_test_database_tx_001",
			SessionID: int64(1),
			ToolName:  "database",
			Arguments: argumentsStr,
			Status:    dao_tool_call_task.ToolCallTaskStatusPending,
		}

		// 开启事务
		tx := dao.GetDb(ctx)

		// 测试事务中插入
		id, err := dao_tool_call_task.ToolCallTaskBusinessIns.InsertInTx(ctx, tx, task)
		assert.NoError(t, err)
		assert.Greater(t, id, int64(0))

		// 验证插入结果
		result, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, task.CallID, result.CallID)
		assert.Equal(t, task.SessionID, result.SessionID)
		assert.Equal(t, task.ToolName, result.ToolName)
	})
}

func TestToolCallTaskQueries(t *testing.T) {
	setUpAll()
	defer tearDownAll()

	t.Run("TestGetMapByPrimaryKeys", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试任务
		var taskIDs []int64
		for i := 0; i < 3; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_map_test_%d", i),
				SessionID: int64(i),
				ToolName:  "filesystem",
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			id, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
			taskIDs = append(taskIDs, id)
		}

		// 测试GetMapByPrimaryKeys
		tasksMap, err := dao_tool_call_task.ToolCallTaskBusinessIns.GetMapByPrimaryKeys(ctx, taskIDs)
		assert.NoError(t, err)
		assert.Equal(t, 3, len(tasksMap))

		// 验证所有任务都在映射中
		for _, id := range taskIDs {
			task, exists := tasksMap[id]
			assert.True(t, exists)
			assert.NotNil(t, task)
			assert.Equal(t, id, task.TaskID)
		}

		// 测试空切片
		emptyMap, err := dao_tool_call_task.ToolCallTaskBusinessIns.GetMapByPrimaryKeys(ctx, []int64{})
		assert.NoError(t, err)
		assert.Equal(t, 0, len(emptyMap))
	})

	t.Run("TestUpdate", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试任务
		arguments := dao.JSONData{
			"path": "/home/<USER>",
		}
		argumentsBytes, err := json.Marshal(arguments)
		assert.NoError(t, err)
		argumentsStr := string(argumentsBytes)
		task := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_update_test",
			SessionID: int64(1),
			ToolName:  "filesystem",
			Arguments: argumentsStr,
			Status:    dao_tool_call_task.ToolCallTaskStatusPending,
		}

		id, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
		assert.NoError(t, err)

		// 更新任务数据
		newArguments := dao.JSONData{
			"path":      "/home/<USER>/documents",
			"recursive": true,
		}
		newResult := dao.JSONData{
			"success": true,
			"count":   5,
		}
		newArgumentsBytes, err := json.Marshal(newArguments)
		assert.NoError(t, err)
		newArgumentsStr := string(newArgumentsBytes)
		newResultBytes, err := json.Marshal(newResult)
		assert.NoError(t, err)
		newResultStr := string(newResultBytes)

		updateData := &dao_tool_call_task.ObjToolCallTask{
			TaskID:       id,
			CallID:       "call_update_test_modified",
			SessionID:    int64(1),
			ToolName:     "filesystem_v2",
			Arguments:    newArgumentsStr,
			Result:       newResultStr,
			OldEnvMd5:    "old_md5_123",
			NewEnvMd5:    "new_md5_456",
			OldEnvUrl:    "http://old.env.url",
			NewEnvUrl:    "http://new.env.url",
			Status:       dao_tool_call_task.ToolCallTaskStatusRunning,
			ErrorMessage: "test error message",
		}

		// 执行更新
		err = dao_tool_call_task.ToolCallTaskBusinessIns.Update(ctx, updateData)
		assert.NoError(t, err)

		// 验证更新结果
		updatedTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, "call_update_test_modified", updatedTask.CallID)
		assert.Equal(t, int64(1), updatedTask.SessionID)
		assert.Equal(t, "filesystem_v2", updatedTask.ToolName)
		assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusRunning, updatedTask.Status)
		assert.Equal(t, "old_md5_123", updatedTask.OldEnvMd5)
		assert.Equal(t, "new_md5_456", updatedTask.NewEnvMd5)
		assert.Equal(t, "test error message", updatedTask.ErrorMessage)
	})

	t.Run("TestUpdateResult", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试任务
		task := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_result_test",
			SessionID: int64(1),
			ToolName:  "filesystem",
			Status:    dao_tool_call_task.ToolCallTaskStatusRunning,
			OldEnvMd5: "old_md5_789",
		}

		id, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
		assert.NoError(t, err)

		// 更新任务结果
		result := dao.JSONData{
			"operation": "list_files",
			"files": []string{
				"/home/<USER>/file1.txt",
				"/home/<USER>/file2.txt",
			},
			"total_size": 2048,
		}
		newEnvMd5 := "new_md5_result_test"
		newEnvUrl := "http://new.env.result.url"

		err = dao_tool_call_task.ToolCallTaskBusinessIns.UpdateMap(ctx, id, map[string]any{
			"result":           result,
			"new_env_md5":      newEnvMd5,
			"new_env_url":      newEnvUrl,
			"tool_call_status": dao_tool_call_task.ToolCallTaskStatusSuccess,
			"completed_at":     time.Now(),
		})
		assert.NoError(t, err)

		// 验证结果更新
		updatedTask, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.NoError(t, err)
		assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusSuccess, updatedTask.Status)
		assert.Equal(t, newEnvMd5, updatedTask.NewEnvMd5)
		assert.Equal(t, newEnvUrl, updatedTask.NewEnvUrl)
		assert.NotNil(t, updatedTask.Result)
		assert.NotNil(t, updatedTask.CompletedAt)
	})

	t.Run("TestDeleteInTx", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建测试任务
		task := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_delete_tx_test",
			SessionID: int64(1),
			ToolName:  "filesystem",
			Status:    dao_tool_call_task.ToolCallTaskStatusPending,
		}

		id, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
		assert.NoError(t, err)

		// 在事务中删除
		tx := dao.GetDb(ctx)
		err = dao_tool_call_task.ToolCallTaskBusinessIns.DeleteInTx(ctx, tx, id)
		assert.NoError(t, err)

		// 验证删除
		_, err = dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPrimaryKey(ctx, id)
		assert.Error(t, err)
	})

	t.Run("TestSelectByToolName", func(t *testing.T) {
		setUp()
		defer tearDown()

		toolName := "database_tool"
		// 创建同一工具的多个任务
		for i := 0; i < 4; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_tool_%d", i),
				SessionID: int64(i),
				ToolName:  toolName,
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
		}

		// 创建其他工具的任务
		otherTask := &dao_tool_call_task.ObjToolCallTask{
			CallID:    "call_other_tool",
			SessionID: int64(1),
			ToolName:  "filesystem_tool",
			Status:    dao_tool_call_task.ToolCallTaskStatusPending,
		}
		_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, otherTask)
		assert.NoError(t, err)

		// 测试按工具名称查询
		tasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByToolName(ctx, toolName)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(tasks), 4)

		// 验证查询结果都是指定工具的任务
		for _, task := range tasks {
			assert.Equal(t, toolName, task.ToolName)
		}
	})

	t.Run("TestSelectBySessionID", func(t *testing.T) {
		setUp()
		defer tearDown()

		sessionID := int64(1)
		// 创建同一会话的多个任务
		for i := 0; i < 3; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_session_test_%d", i),
				SessionID: int64(i),
				ToolName:  "filesystem",
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
		}

		// 测试按会话ID查询
		tasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectBySessionID(ctx, sessionID)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(tasks), 3)

		// 验证查询结果
		for _, task := range tasks {
			assert.Equal(t, sessionID, task.SessionID)
		}
	})

	t.Run("TestSelectByStatus", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建不同状态的任务
		for i := 0; i < 3; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_status_test_%d", i),
				SessionID: int64(i),
				ToolName:  "filesystem",
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
		}

		// 测试按状态查询
		pendingTasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByStatus(ctx, dao_tool_call_task.ToolCallTaskStatusPending)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(pendingTasks), 3)

		// 验证查询结果
		for _, task := range pendingTasks {
			assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusPending, task.Status)
		}
	})

	t.Run("TestSelectByPage", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建多个测试任务
		for i := 0; i < 5; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_page_test_%d", i),
				SessionID: int64(i),
				ToolName:  "filesystem",
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
		}

		// 测试分页查询
		total, tasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectByPage(ctx, 1, 10, "", "", "")
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(5))
		assert.GreaterOrEqual(t, len(tasks), 5)
	})

	t.Run("TestSelectPendingTasks", func(t *testing.T) {
		setUp()
		defer tearDown()

		// 创建待处理任务
		for i := 0; i < 3; i++ {
			task := &dao_tool_call_task.ObjToolCallTask{
				CallID:    fmt.Sprintf("call_pending_test_%d", i),
				SessionID: int64(i),
				ToolName:  "filesystem",
				Status:    dao_tool_call_task.ToolCallTaskStatusPending,
			}
			_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
			assert.NoError(t, err)
		}

		// 测试查询待处理任务
		pendingTasks, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectPendingTasks(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(pendingTasks), 3)

		// 验证所有返回的任务都是待处理状态
		for _, task := range pendingTasks {
			assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusPending, task.Status)
		}
	})

	t.Run("TestSelectTaskStatistics", func(t *testing.T) {
		setUp()
		defer tearDown()

		sessionID := time.Now().UnixMilli()
		// 创建不同状态的任务进行统计
		statusCounts := map[dao_tool_call_task.ToolCallTaskStatus]int{
			dao_tool_call_task.ToolCallTaskStatusPending: 3,
			dao_tool_call_task.ToolCallTaskStatusRunning: 2,
			dao_tool_call_task.ToolCallTaskStatusSuccess: 4,
			dao_tool_call_task.ToolCallTaskStatusFailed:  1,
		}

		for status, count := range statusCounts {
			for i := 0; i < count; i++ {
				task := &dao_tool_call_task.ObjToolCallTask{
					CallID:    fmt.Sprintf("call_stats_%s_%d", status, i),
					SessionID: sessionID,
					ToolName:  "filesystem",
					Status:    status,
				}
				_, err := dao_tool_call_task.ToolCallTaskBusinessIns.Insert(ctx, task)
				assert.NoError(t, err)
			}
		}

		// 测试查询统计信息（按会话ID）
		statistics, err := dao_tool_call_task.ToolCallTaskBusinessIns.SelectTaskStatistics(ctx, sessionID)
		assert.NoError(t, err)
		assert.Equal(t, int64(3), statistics[string(dao_tool_call_task.ToolCallTaskStatusPending)])
		assert.Equal(t, int64(2), statistics[string(dao_tool_call_task.ToolCallTaskStatusRunning)])
		assert.Equal(t, int64(4), statistics[string(dao_tool_call_task.ToolCallTaskStatusSuccess)])
		assert.Equal(t, int64(1), statistics[string(dao_tool_call_task.ToolCallTaskStatusFailed)])
	})
}
