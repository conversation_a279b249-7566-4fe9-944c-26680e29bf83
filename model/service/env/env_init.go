package service

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/environment"
)

// 全局互斥锁，确保环境初始化接口单线程执行
var initEnvMutex sync.Mutex

type InitEnvInputData struct {
	Name                  string `json:"name" validate:"required"`
	Description           string `json:"description" validate:"required"`
	EnvironmentDependency []struct {
		Path    string `json:"path" validate:"required"`
		Type    string `json:"type" validate:"required,oneof=directory db url file cmd delay_cmd"`
		Content string `json:"content"`
		Name    string `json:"name"`
	} `json:"environment_dependency" validate:"required"`
}

type InitEnvOutputData struct {
	EnvID    int64  `json:"env_id"`
	BosURL   string `json:"bos_url"`
	MD5      string `json:"md5"`
	FileSize int64  `json:"file_size"`
}

type InitEnv struct {
	base.Service[InitEnvInputData, InitEnvOutputData]
}

func (ser *InitEnv) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {

	// 确保环境初始化接口单线程执行
	initEnvMutex.Lock()
	defer initEnvMutex.Unlock()

	workDir := env.DataDir() + "/init/temp_" + strconv.FormatInt(time.Now().UnixMilli(), 10)
	err = os.MkdirAll(workDir, 0755)
	if err != nil {
		resource.LoggerService.Warning(ctx, "创建临时目录失败: "+err.Error())
		return nil, nil, err
	}
	envDependency := dao.JSONData{
		"environment_dependency": ser.InputData.EnvironmentDependency,
	}
	if len(ser.InputData.EnvironmentDependency) > 0 {
		// 创建环境管理器
		envManager := environment.DefaultManager()

		// 转换数据结构
		var envDeps []environment.EnvironmentDependency
		for _, dep := range ser.InputData.EnvironmentDependency {
			envDeps = append(envDeps, environment.EnvironmentDependency{
				Path:    dep.Path,
				Type:    dep.Type,
				Content: dep.Content,
				Name:    dep.Name,
			})
		}

		// 执行环境初始化
		results, err := envManager.InitializeInWorkDir(ctx, envDeps, workDir)
		if err != nil {
			return nil, nil, err
		}

		// 检查是否有失败的初始化
		var failedResults []environment.InitializationResult
		for _, result := range results {
			if !result.Success {
				failedResults = append(failedResults, result)
			}
		}
		if len(failedResults) > 0 {
			// 构造详细的错误信息
			var errorDetails []string
			for _, result := range failedResults {
				detail := fmt.Sprintf("类型: %s, 路径: %s, 错误: %s", result.Type, result.Path, result.ErrorMessage)
				errorDetails = append(errorDetails, detail)
			}
			return nil, nil, &lib_error.CustomErr{Code: errcode.EnvironmentInitFailed,
				Msg: "环境依赖初始化失败：" + strings.Join(errorDetails, "; ")}
		}
	}

	bosResult, err := dao_rpc_bos.PackageDirectoryToBos(ctx, workDir, ser.InputData.Name, ser.InputData.Description, envDependency)
	if err != nil {
		resource.LoggerService.Warning(ctx, "上传环境目录到BOS失败: "+err.Error())
		return nil, nil, err
	}

	// 删除临时目录
	defer os.RemoveAll(workDir)

	// 返回成功响应
	response := InitEnvOutputData{
		EnvID:    bosResult.EnvID,
		BosURL:   bosResult.BosURL,
		MD5:      bosResult.MD5,
		FileSize: bosResult.FileSize,
	}

	return response, nil, nil
}
