package service

import (
	"context"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
)

type RegisterMcpServerInputData struct {
	ServerName           string         `json:"server_name" validate:"required"`
	Description          string         `json:"description" validate:"omitempty"`
	Type                 string         `json:"type" validate:"omitempty,oneof=local remote sse"`
	Command              string         `json:"command" validate:"omitempty"`
	Args                 []string       `json:"args" validate:"omitempty,dive,required"`
	Url                  string         `json:"url" validate:"omitempty,url"`
	Headers              map[string]any `json:"headers" validate:"omitempty"`
	MockMcpServerCodeURL string         `json:"mock_mcp_server_code_url" validate:"omitempty,url"`
}

type RegisterMcpServerOutputData struct {
	ServerID   int64  `json:"server_id"`
	ServerName string `json:"server_name"`
	Status     string `json:"status"`
}

type RegisterMcpSever struct {
	base.Service[RegisterMcpServerInputData, RegisterMcpServerOutputData]
}

func (ser *RegisterMcpSever) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	typeValue := dao_register_mcp_server.TypeLocal
	if ser.InputData.Type != "" {
		typeValue = dao_register_mcp_server.Type(ser.InputData.Type)
	}

	// 创建服务器记录
	data := &dao_register_mcp_server.ObjRegisterMcpServer{
		ServerName:       ser.InputData.ServerName,
		Type:             typeValue,
		Url:              ser.InputData.Url,
		Command:          ser.InputData.Command,
		Args:             ser.InputData.Args,
		Description:      ser.InputData.Description,
		ServerCodeBosUrl: ser.InputData.MockMcpServerCodeURL,
		Headers:          dao.JSONData(ser.InputData.Headers),
	}

	serverID, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.Insert(ctx, data)
	if err != nil {
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.McpServerInitFailed,
			Msg:  "MCP服务器初始化失败",
		}
	}

	// 返回成功响应
	response := &RegisterMcpServerOutputData{
		ServerID:   serverID,
		ServerName: ser.InputData.ServerName,
		Status:     "registed",
	}

	return response, nil, nil
}
