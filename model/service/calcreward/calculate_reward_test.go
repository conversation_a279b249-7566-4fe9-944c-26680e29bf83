package service_test

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/bootstrap"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/reward_calculator"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/calcreward"
	tool_common "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"
)

var ctx = context.Background()
var bootstrapOnceSync sync.Once

func bootstrapOnce() {
	bootstrap.MustInitTest(ctx)
}

func setUpAll(t *testing.T) {
	bootstrapOnceSync.Do(bootstrapOnce)
}

func tearDownAll(t *testing.T) {}

// TestCalculateReward_Execute_Success 测试成功计算奖励的情况
func TestCalculateReward_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1001,
		JobID:           "job_test_reward_001",
		SessionCode:     fmt.Sprintf("test_session_reward_%d", time.Now().UnixNano()),
		EnvID:           2001,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态，这样会执行计算逻辑
	}

	// 创建测试环境，包含reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path",
				"type":    "delay_cmd",
				"name":    "reward",
				"content": "echo 0.85",
			},
			{
				"path":    "/test/path2",
				"type":    "file",
				"name":    "config",
				"content": "test config",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2001,
		EnvMd5:        "test_env_md5_reward",
		Name:          "test_env_reward",
		Description:   "测试奖励环境",
		BosURL:        "https://bos.example.com/envs/test_reward.tar.gz",
		EnvDependency: envDependency,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock RCC配置读取
	patches.ApplyFunc(rcc.GetValue, func(key string, defaultValue string) string {
		switch key {
		case "k8s_proxy.init.source_type":
			return "mcp"
		default:
			return defaultValue
		}
	})

	// Mock RCC StartWithConfFile to avoid initialization issues
	patches.ApplyFunc(rcc.StartWithConfFile, func(ctx context.Context, confPath string) error {
		return nil
	})

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2001 {
				return env, nil
			}
			return nil, nil
		})

	// Mock k8s_proxy执行命令
	patches.ApplyMethod(reflect.TypeOf(dao_rpc_k8s_proxy.K8sProxyClientIns), "ExecCommand",
		func(_ *dao_rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *dao_rpc_k8s_proxy.ExecCommandRequest) (*dao_rpc_k8s_proxy.ExecCommandResponse, error) {
			// 验证请求参数
			assert.Equal(t, "mcp", req.SourceType)
			assert.Equal(t, []string{"sh", "-c", "echo 0.85"}, req.Command)
			assert.Equal(t, "job_test_reward_001", req.JobName)

			return &dao_rpc_k8s_proxy.ExecCommandResponse{
				Stdout: "0.85",
			}, nil
		})

	// Mock UpdateMap方法
	patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "UpdateMap",
		func(_ dao_session.SessionBusiness, ctx context.Context, sessionID int64, updates map[string]any) error {
			return nil
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1001,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Equal(t, "0.85", output.Reward)
}

// TestCalculateReward_Execute_WithSessionCode 测试使用SessionCode计算奖励的情况
func TestCalculateReward_Execute_WithSessionCode(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	sessionCode := fmt.Sprintf("test_session_reward_code_%d", time.Now().UnixNano())
	session := &dao_session.ObjSession{
		SessionID:       1002,
		JobID:           "job_test_reward_002",
		SessionCode:     sessionCode,
		EnvID:           2002,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 创建测试环境，包含reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path",
				"type":    "delay_cmd",
				"name":    "reward",
				"content": "echo 0.92",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2002,
		EnvMd5:        "test_env_md5_reward_2",
		Name:          "test_env_reward_2",
		Description:   "测试奖励环境2",
		BosURL:        "https://bos.example.com/envs/test_reward_2.tar.gz",
		EnvDependency: envDependency,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock RCC配置读取
	patches.ApplyFunc(rcc.GetValue, func(key string, defaultValue string) string {
		switch key {
		case "k8s_proxy.init.source_type":
			return "mcp"
		default:
			return defaultValue
		}
	})

	// Mock RCC StartWithConfFile to avoid initialization issues
	patches.ApplyFunc(rcc.StartWithConfFile, func(ctx context.Context, confPath string) error {
		return nil
	})

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2002 {
				return env, nil
			}
			return nil, nil
		})

	// Mock k8s_proxy执行命令
	patches.ApplyMethod(reflect.TypeOf(dao_rpc_k8s_proxy.K8sProxyClientIns), "ExecCommand",
		func(_ *dao_rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *dao_rpc_k8s_proxy.ExecCommandRequest) (*dao_rpc_k8s_proxy.ExecCommandResponse, error) {
			return &dao_rpc_k8s_proxy.ExecCommandResponse{
				Stdout: "0.92",
			}, nil
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionCode: sessionCode,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Equal(t, "0.92", output.Reward)
}

// TestCalculateReward_Execute_SessionNotFound 测试Session不存在的情况
func TestCalculateReward_Execute_SessionNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法返回错误
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return nil, assert.AnError
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   999999, // 不存在的SessionID
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
}

// TestCalculateReward_Execute_MissingSessionIDAndCode 测试既没有SessionID也没有SessionCode的情况
func TestCalculateReward_Execute_MissingSessionIDAndCode(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   0,
		SessionCode: "",
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "session_id 和 session_code 必须提供一个")
}

// TestCalculateReward_Execute_EnvNotFound 测试环境不存在的情况
func TestCalculateReward_Execute_EnvNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1003,
		JobID:           "job_test_reward_003",
		SessionCode:     fmt.Sprintf("test_session_reward_003_%d", time.Now().UnixNano()),
		EnvID:           999999, // 不存在的环境ID
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询返回错误
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			return nil, fmt.Errorf("数据库查询错误")
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1003,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
}

// TestCalculateReward_Execute_EmptyEnvDependency 测试环境依赖为空的情况
func TestCalculateReward_Execute_EmptyEnvDependency(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1004,
		JobID:           "job_test_reward_004",
		SessionCode:     fmt.Sprintf("test_session_reward_004_%d", time.Now().UnixNano()),
		EnvID:           2004,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 创建测试环境，环境依赖为空
	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2004,
		EnvMd5:        "test_env_md5_reward_4",
		Name:          "test_env_reward_4",
		Description:   "测试奖励环境4",
		BosURL:        "https://bos.example.com/envs/test_reward_4.tar.gz",
		EnvDependency: dao_base.JSONData{}, // 空的环境依赖
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2004 {
				return env, nil
			}
			return nil, fmt.Errorf("环境不存在")
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1004,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "环境依赖配置中没有environment_dependency字段")
}

// TestCalculateReward_Execute_MissingEnvironmentDependencyKey 测试环境依赖缺少environment_dependency字段的情况
func TestCalculateReward_Execute_MissingEnvironmentDependencyKey(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1005,
		JobID:           "job_test_reward_005",
		SessionCode:     fmt.Sprintf("test_session_reward_005_%d", time.Now().UnixNano()),
		EnvID:           2005,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 创建测试环境，缺少environment_dependency字段
	envDependency := dao_base.JSONData{
		"other_config": "some_value",
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2005,
		EnvMd5:        "test_env_md5_reward_5",
		Name:          "test_env_reward_5",
		Description:   "测试奖励环境5",
		BosURL:        "https://bos.example.com/envs/test_reward_5.tar.gz",
		EnvDependency: envDependency,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2005 {
				return env, nil
			}
			return nil, fmt.Errorf("环境不存在")
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1005,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "环境依赖配置中没有environment_dependency字段")
}

// TestCalculateReward_Execute_NoRewardCommand 测试环境中没有reward命令的情况
func TestCalculateReward_Execute_NoRewardCommand(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1006,
		JobID:           "job_test_reward_006",
		SessionCode:     fmt.Sprintf("test_session_reward_006_%d", time.Now().UnixNano()),
		EnvID:           2006,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 创建测试环境，没有reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path",
				"type":    "file",
				"name":    "config",
				"content": "test config",
			},
			{
				"path":    "/test/path2",
				"type":    "delay_cmd",
				"name":    "init",
				"content": "echo init",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2006,
		EnvMd5:        "test_env_md5_reward_6",
		Name:          "test_env_reward_6",
		Description:   "测试奖励环境6",
		BosURL:        "https://bos.example.com/envs/test_reward_6.tar.gz",
		EnvDependency: envDependency,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2006 {
				return env, nil
			}
			return nil, fmt.Errorf("环境不存在")
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1006,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "未找到reward命令")
}

// TestCalculateReward_Execute_K8sProxyError 测试k8s_proxy执行命令失败的情况
func TestCalculateReward_Execute_K8sProxyError(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		JobID:           "job_test_reward_007",
		SessionCode:     fmt.Sprintf("test_session_reward_007_%d", time.Now().UnixNano()),
		EnvID:           2007,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// 创建测试环境，包含reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path",
				"type":    "delay_cmd",
				"name":    "reward",
				"content": "echo 0.75",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvMd5:        "test_env_md5_reward_7",
		Name:          "test_env_reward_7",
		Description:   "测试奖励环境7",
		BosURL:        "https://bos.example.com/envs/test_reward_7.tar.gz",
		EnvDependency: envDependency,
	}

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2007 {
				return env, nil
			}
			return nil, nil
		})

	// Mock k8s_proxy执行命令失败
	patches.ApplyMethod(reflect.TypeOf(dao_rpc_k8s_proxy.K8sProxyClientIns), "ExecCommand",
		func(_ *dao_rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *dao_rpc_k8s_proxy.ExecCommandRequest) (*dao_rpc_k8s_proxy.ExecCommandResponse, error) {
			return nil, assert.AnError
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1007, // 使用固定的SessionID避免数据库插入
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)
}

// TestCalculateReward_Execute_DefaultTimeout 测试默认超时时间
func TestCalculateReward_Execute_DefaultTimeout(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1008,
		JobID:           "job_test_reward_008",
		SessionCode:     fmt.Sprintf("test_session_reward_008_%d", time.Now().UnixNano()),
		EnvID:           2008,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped, // 改为stopped状态
	}

	// 创建测试环境，包含reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path",
				"type":    "delay_cmd",
				"name":    "reward",
				"content": "echo 0.65",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2008,
		EnvMd5:        "test_env_md5_reward_8",
		Name:          "test_env_reward_8",
		Description:   "测试奖励环境8",
		BosURL:        "https://bos.example.com/envs/test_reward_8.tar.gz",
		EnvDependency: envDependency,
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock RCC配置读取
	patches.ApplyFunc(rcc.GetValue, func(key string, defaultValue string) string {
		switch key {
		case "k8s_proxy.init.source_type":
			return "mcp"
		default:
			return defaultValue
		}
	})

	// Mock RCC StartWithConfFile to avoid initialization issues
	patches.ApplyFunc(rcc.StartWithConfFile, func(ctx context.Context, confPath string) error {
		return nil
	})

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2008 {
				return env, nil
			}
			return nil, fmt.Errorf("环境不存在")
		})

	// Mock k8s_proxy执行命令
	patches.ApplyMethod(reflect.TypeOf(dao_rpc_k8s_proxy.K8sProxyClientIns), "ExecCommand",
		func(_ *dao_rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *dao_rpc_k8s_proxy.ExecCommandRequest) (*dao_rpc_k8s_proxy.ExecCommandResponse, error) {
			// 验证默认超时时间为30秒
			assert.Equal(t, "mcp", req.SourceType)
			assert.Equal(t, []string{"sh", "-c", "echo 0.65"}, req.Command)
			assert.Equal(t, "job_test_reward_008", req.JobName)

			return &dao_rpc_k8s_proxy.ExecCommandResponse{
				Stdout: "0.65",
			}, nil
		})

	// Mock UpdateMap方法
	patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "UpdateMap",
		func(_ dao_session.SessionBusiness, ctx context.Context, sessionID int64, updates map[string]any) error {
			return nil
		})

	// 准备测试服务（不设置TimeoutSecs，应该使用默认值30）
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID: 1008,
		// 不设置TimeoutSecs
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Equal(t, "0.65", output.Reward)
}

// TestCalculateReward_Execute_FromDatabase 测试从数据库读取reward字段的情况
func TestCalculateReward_Execute_FromDatabase(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session，已经有reward值
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	rewardValue := "0.95"
	session := &dao_session.ObjSession{
		SessionID:       1009,
		JobID:           "job_test_reward_009",
		SessionCode:     fmt.Sprintf("test_session_reward_db_%d", time.Now().UnixNano()),
		EnvID:           2009,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped,
		Reward:          dao_base.JSONData{
			"reward": rewardValue,
		}, // 已经有reward值
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1009,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Equal(t, "0.95", output.Reward)
	assert.Empty(t, output.SessionStatus) // 从数据库读取时不返回session_status
}

// TestCalculateReward_Execute_RunningStatus 测试session为running状态且reward为空的情况
func TestCalculateReward_Execute_RunningStatus(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session，running状态，reward为空
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1010,
		JobID:           "job_test_reward_010",
		SessionCode:     fmt.Sprintf("test_session_reward_running_%d", time.Now().UnixNano()),
		EnvID:           2010,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusRunning,
		Reward:          nil, // reward为空
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// 创建测试环境，但没有reward命令
	envDependency := dao_base.JSONData{
		"environment_dependency": []map[string]interface{}{
			{
				"path":    "/test/path2",
				"type":    "file",
				"name":    "config",
				"content": "test config",
			},
		},
	}

	env := &dao_mcp_env.ObjMcpEnv{
		EnvID:         2010,
		EnvMd5:        "test_env_md5_running",
		Name:          "test_env_running",
		Description:   "测试running环境",
		BosURL:        "https://bos.example.com/envs/test_running.tar.gz",
		EnvDependency: envDependency,
	}

	// Mock 环境查询
	patches.ApplyMethod(reflect.TypeOf(dao_mcp_env.McpEnvBusinessIns), "SelectByPrimaryKey",
		func(_ dao_mcp_env.McpEnvBusiness, ctx context.Context, id int64) (*dao_mcp_env.ObjMcpEnv, error) {
			if id == 2010 {
				return env, nil
			}
			return nil, fmt.Errorf("MCP环境不存在")
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1010,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Empty(t, output.Reward)
	assert.Equal(t, "running", output.SessionStatus)
}

// TestCalculateReward_Execute_StoppingStatus 测试session为stopping状态且reward为空的情况
func TestCalculateReward_Execute_StoppingStatus(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session，stopping状态，reward为空
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1011,
		JobID:           "job_test_reward_011",
		SessionCode:     fmt.Sprintf("test_session_reward_stopping_%d", time.Now().UnixNano()),
		EnvID:           2011,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopping,
		Reward:          nil, // reward为空
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1011,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Empty(t, output.Reward)
	assert.Equal(t, "stopping", output.SessionStatus)
}

// TestCalculateReward_Execute_RunningSync 测试session为running状态的同步计算
func TestCalculateReward_Execute_RunningSync(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session，running状态，reward为空
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		SessionID:       1012,
		JobID:           "job_test_reward_012",
		SessionCode:     fmt.Sprintf("test_session_reward_running_sync_%d", time.Now().UnixNano()),
		EnvID:           2012,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusRunning,
		Reward:          nil, // reward为空
	}

	// 设置mock
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock ValidateSession方法
	patches.ApplyMethod(reflect.TypeOf(&tool_common.ToolCallCommon{}), "ValidateSession",
		func(_ *tool_common.ToolCallCommon, ctx context.Context, sessionID int64, sessionCode string) (*dao_session.ObjSession, error) {
			return session, nil
		})

	// Mock reward calculator
	patches.ApplyMethod(reflect.TypeOf(&reward_calculator.RewardCalculator{}), "CalculateReward",
		func(_ *reward_calculator.RewardCalculator, ctx context.Context, session *dao_session.ObjSession, cmd string, timeoutSecs int) (string, string, error) {
			return "0.92", "", nil
		})

	// Mock UpdateMap方法
	patches.ApplyMethod(reflect.TypeOf(&dao_session.SessionBusinessIns), "UpdateMap",
		func(_ *dao_session.SessionBusiness, ctx context.Context, sessionID int64, updates map[string]any) error {
			return nil
		})

	// 准备测试服务
	rewardService := &service.CalculateRewardSever{}
	rewardService.InputData = &service.CalculateRewardInputData{
		SessionID:   1012,
		TimeoutSecs: 30,
	}

	// 执行测试
	resp, _, err := rewardService.Execute(ctx, nil)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.CalculateRewardOutputData)
	assert.True(t, ok)
	assert.Equal(t, "0.92", output.Reward)
	assert.Empty(t, output.SessionStatus) // 同步计算完成，不返回状态
}

// TestCalculateReward_Execute_RunningStatusConstants 测试状态常量定义
func TestCalculateReward_Execute_RunningStatusConstants(t *testing.T) {
	// 验证状态常量定义
	assert.Equal(t, "running", string(dao_session.ContainerStatusRunning))
	assert.Equal(t, "stopping", string(dao_session.ContainerStatusStopping))
	assert.Equal(t, "stopped", string(dao_session.ContainerStatusStopped))
}
