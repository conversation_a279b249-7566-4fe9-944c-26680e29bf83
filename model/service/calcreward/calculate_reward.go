package service

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	"icode.baidu.com/baidu/gdp/ghttp"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/reward_calculator"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	tool_common "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
)

type CalculateRewardInputData struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
	Cmd         string `json:"cmd,omitempty"`
	TimeoutSecs int    `json:"timeout_seconds,omitempty"`
}

type CalculateRewardOutputData struct {
	Reward        *string `json:"reward"`        // 从BOS拉取的stdout内容
	ExecCode      *int    `json:"exec_code"`     // k8s执行返回码
	ErrLog        *string `json:"err_log"`       // 从BOS拉取的stderr内容
	SessionStatus string `json:"session_status,omitempty"`
}

type CalculateRewardSever struct {
	base.Service[CalculateRewardInputData, CalculateRewardOutputData]
}

// Execute 执行同步工具调用逻辑
func (s *CalculateRewardSever) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	if input.SessionID <= 0 && input.SessionCode == "" {
		return nil, nil, &lib_error.CustomErr{Code: errcode.UserInputError, Msg: "session_id 和 session_code 必须提供一个"}
	}
	if input.TimeoutSecs <= 0 {
		input.TimeoutSecs = 300
	}

	// 验证Session有效性
	common := &tool_common.ToolCallCommon{}
	session, err := common.ValidateSession(ctx, input.SessionID, input.SessionCode)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("Session验证失败: %v", err))
		return nil, nil, err
	}

	// 1. 优先从数据库读取reward字段
	if len(session.Reward) > 0 {
		// 解析reward JSON数据
		var rewardData dao_session.RewardData
		rewardJSON, err := json.Marshal(session.Reward)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("序列化奖励数据失败: session_id=%d, error=%v", input.SessionID, err))
			return nil, nil, err
		}
		
		err = json.Unmarshal(rewardJSON, &rewardData)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("反序列化奖励数据失败: session_id=%d, error=%v", input.SessionID, err))
			return nil, nil, err
		}

		// 从BOS拉取内容并构建返回数据
		output, err := s.buildOutputFromRewardData(ctx, input.SessionID, &rewardData)
		if err != nil {
			return nil, nil, err
		}
		return output, nil, nil
	}

	// 2. 如果是stopping状态，返回特殊响应
	if session.ContainerStatus == dao_session.ContainerStatusStopping {
		output := &CalculateRewardOutputData{
			Reward:        nil,
			ExecCode:      nil,
			ErrLog:        nil,
			SessionStatus: string(session.ContainerStatus),
		}
		return output, nil, nil
	}

	// 3. 如果是running或stopped状态，直接同步计算奖励
	if session.ContainerStatus == dao_session.ContainerStatusRunning {
		// 使用同步方式计算奖励
		calculator := reward_calculator.NewRewardCalculator()
		rewardJSON, err := calculator.CalculateReward(ctx, session, input.Cmd, input.TimeoutSecs)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("计算奖励失败: session_id=%d, error=%v", input.SessionID, err))
			// 对于running状态且未找到reward命令的情况，返回特殊响应而不是错误
			if session.ContainerStatus == dao_session.ContainerStatusRunning && strings.Contains(err.Error(), "未找到reward命令") {
				output := &CalculateRewardOutputData{
					Reward:        nil,
					ExecCode:      nil,
					ErrLog:        nil,
					SessionStatus: string(session.ContainerStatus),
				}
				return output, nil, nil
			}
			return nil, nil, err
		}

		// 更新reward字段到数据库（存储JSON数据）
		err = dao_session.SessionBusinessIns.UpdateMap(ctx, input.SessionID, map[string]any{
			"reward": rewardJSON,
		})
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("更新reward字段失败: session_id=%d, error=%v", input.SessionID, err))
			return nil, nil, err
		}

		// 解析新计算的奖励数据并构建返回结果
		var rewardData dao_session.RewardData
		err = json.Unmarshal([]byte(rewardJSON), &rewardData)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("解析新计算的奖励数据失败: session_id=%d, error=%v", input.SessionID, err))
			return nil, nil, err
		}

		// 从BOS拉取内容并构建返回数据
		output, err := s.buildOutputFromRewardData(ctx, input.SessionID, &rewardData)
		if err != nil {
			return nil, nil, err
		}
		return output, nil, nil
	}

	// 4. 其他状态（如stopped、failed等）不支持计算奖励
	return nil, nil, &lib_error.CustomErr{
		Code: errcode.UserInputError,
		Msg:  fmt.Sprintf("Session状态为%s，不支持计算奖励", session.ContainerStatus),
	}
}

// buildOutputFromRewardData 从RewardData构建输出数据，从BOS拉取实际内容
func (s *CalculateRewardSever) buildOutputFromRewardData(ctx context.Context, sessionID int64, rewardData *dao_session.RewardData) (*CalculateRewardOutputData, error) {
	// 从BOS拉取stdout内容作为reward字段
	stdoutContent := ""
	if rewardData.StdoutURL != "" {
		content, err := s.downloadFromURL(ctx, rewardData.StdoutURL)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("从BOS拉取stdout内容失败: session_id=%d, url=%s, error=%v", sessionID, rewardData.StdoutURL, err))
			// 拉取失败时使用空字符串
		} else {
			stdoutContent = content
		}
	}

	// 从BOS拉取stderr内容作为err_log字段
	stderrContent := ""
	if rewardData.StderrURL != "" {
		content, err := s.downloadFromURL(ctx, rewardData.StderrURL)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("从BOS拉取stderr内容失败: session_id=%d, url=%s, error=%v", sessionID, rewardData.StderrURL, err))
			// 拉取失败时使用空字符串
		} else {
			stderrContent = content
		}
	}

	return &CalculateRewardOutputData{
		Reward:   &stdoutContent,
		ExecCode: &rewardData.Code,
		ErrLog:   &stderrContent,
	}, nil
}

// downloadFromURL 通过HTTP直接下载URL内容
func (s *CalculateRewardSever) downloadFromURL(ctx context.Context, url string) (string, error) {
	// 关键修改部分：创建一个自定义的 Transport
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	// 创建HTTP客户端，并使用上面创建的 transport
	// 同时设置超时
	client := &http.Client{
		Transport: tr,
		Timeout:   30 * time.Second,
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP请求返回错误状态: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应内容失败: %w", err)
	}

	return string(body), nil
}
