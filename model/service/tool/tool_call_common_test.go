package tool_test

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool"
)

// createTestToolCallTask 创建测试用的 ToolCallTask 数据
func createTestToolCallTask(taskID int64, callID string, sessionID int64, status dao_tool_call_task.ToolCallTaskStatus) *dao_tool_call_task.ObjToolCallTask {
	now := time.Now()
	return &dao_tool_call_task.ObjToolCallTask{
		TaskID:       taskID,
		CallID:       callID,
		SessionID:    sessionID,
		ToolName:     "test_tool",
		Arguments:    `{"test": "value"}`,
		Status:       status,
		Result:       "test result",
		ErrorMessage: "test error",
		CreatedAt:    &now,
		UpdatedAt:    &now,
	}
}

// createTestToolCallRequest 创建测试用的 ToolCallRequest
func createTestToolCallRequest(sessionID int64, sessionCode string) *tool.ToolCallRequest {
	return &tool.ToolCallRequest{
		SessionID:   sessionID,
		SessionCode: sessionCode,
		Name:        "test_tool",
		Arguments:   `{"test": "value"}`,
		CallID:      uuid.New().String(),
		TimeoutSecs: 30,
	}
}

// mockLogger Mock Logger 服务
func mockLogger(patches *gomonkey.Patches) {
	patches.ApplyMethod(reflect.TypeOf(resource.LoggerService), "Warning",
		func(_ interface{}, ctx context.Context, msg string) {
			// 可以在这里验证日志内容
		})
}

// mockTimeForTesting Mock 时间相关函数（用于加速测试）
func mockTimeForTesting(patches *gomonkey.Patches) {
	patches.ApplyFunc(time.Sleep, func(d time.Duration) {
		// 不实际睡眠，加速测试执行
	})
}

// ===== ValidateSession 测试 =====

func TestValidateSession_Success_Cases(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tests := []struct {
		name        string
		sessionID   int64
		sessionCode string
		setupMock   func(*gomonkey.Patches)
		expectError bool
	}{
		{
			name:        "ValidSessionCode_Success",
			sessionCode: "valid_session_code",
			setupMock: func(patches *gomonkey.Patches) {
				patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "GetSessionByCode",
					func(_ dao_session.SessionBusiness, ctx context.Context, code string) (*dao_session.ObjSession, error) {
						return createTestSessionForCommon(1001, code, dao_session.ContainerStatusRunning), nil
					})
			},
			expectError: false,
		},
		{
			name:      "ValidSessionID_Success",
			sessionID: 1001,
			setupMock: func(patches *gomonkey.Patches) {
				patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "SelectByPrimaryKey",
					func(_ dao_session.SessionBusiness, ctx context.Context, id int64) (*dao_session.ObjSession, error) {
						return createTestSessionForCommon(id, "test_session", dao_session.ContainerStatusRunning), nil
					})
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			tt.setupMock(patches)
			mockLogger(patches)

			tc := &tool.ToolCallCommon{}
			session, err := tc.ValidateSession(ctx, tt.sessionID, tt.sessionCode)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, session)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, session)
				assert.Equal(t, dao_session.ContainerStatusRunning, session.ContainerStatus)
			}
		})
	}
}

// createTestSessionForCommon 创建测试用的 Session 数据（避免与其他文件冲突）
func createTestSessionForCommon(sessionID int64, sessionCode string, status dao_session.ContainerStatus) *dao_session.ObjSession {
	now := time.Now()
	return &dao_session.ObjSession{
		SessionID:       sessionID,
		SessionCode:     sessionCode,
		JobID:           "test-job-" + sessionCode,
		EnvID:           1001,
		ContainerStatus: status,
		TimeoutSeconds:  3600,
		CreatedAt:       &now,
		UpdatedAt:       &now,
	}
}

func TestValidateSession_Error_Cases(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tests := []struct {
		name            string
		sessionID       int64
		sessionCode     string
		setupMock       func(*gomonkey.Patches)
		expectedErrCode int
		expectedErrMsg  string
	}{
		{
			name:        "SessionCode_QueryFailed",
			sessionCode: "invalid_code",
			setupMock: func(patches *gomonkey.Patches) {
				patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "GetSessionByCode",
					func(_ dao_session.SessionBusiness, ctx context.Context, code string) (*dao_session.ObjSession, error) {
						return nil, assert.AnError
					})
			},
			expectedErrCode: errcode.SessionNotFound,
			expectedErrMsg:  "无效的 session_code",
		},
		{
			name:            "BothParameters_Empty",
			sessionID:       0,
			sessionCode:     "",
			setupMock:       func(patches *gomonkey.Patches) {},
			expectedErrCode: errcode.UserInputError,
			expectedErrMsg:  "session_id 或 session_code 必须提供一个",
		},
		{
			name:        "Session_StatusNotRunning",
			sessionCode: "pending_session",
			setupMock: func(patches *gomonkey.Patches) {
				patches.ApplyMethod(reflect.TypeOf(dao_session.SessionBusinessIns), "GetSessionByCode",
					func(_ dao_session.SessionBusiness, ctx context.Context, code string) (*dao_session.ObjSession, error) {
						return createTestSessionForCommon(1001, code, dao_session.ContainerStatusPending), nil
					})
			},
			expectedErrCode: errcode.SQLSelectError,
			expectedErrMsg:  "Session状态异常",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			tt.setupMock(patches)
			mockLogger(patches)

			tc := &tool.ToolCallCommon{}
			session, err := tc.ValidateSession(ctx, tt.sessionID, tt.sessionCode)

			assert.Error(t, err)
			assert.Nil(t, session)

			if tt.expectedErrCode != 0 {
				if customErr, ok := err.(*lib_error.CustomErr); ok {
					assert.Equal(t, tt.expectedErrCode, customErr.Code)
					if tt.expectedErrMsg != "" {
						assert.Contains(t, customErr.Msg, tt.expectedErrMsg)
					}
				}
			}
		})
	}
}

// ===== WaitForTaskCompletion 超时测试 =====

func TestWaitForTaskCompletion_Timeout_Error(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	callCount := 0
	startTime := time.Now()

	// Mock time.Now 来模拟时间流逝
	patches.ApplyFunc(time.Now, func() time.Time {
		return startTime
	})

	// Mock time.Since 来模拟超时
	patches.ApplyFunc(time.Since, func(t time.Time) time.Duration {
		callCount++
		if callCount > 2 {
			return 35 * time.Second // 超过30秒超时
		}
		return 1 * time.Second
	})

	// Mock SelectByCallID 返回运行中的任务
	patches.ApplyMethod(reflect.TypeOf(dao_tool_call_task.ToolCallTaskBusinessIns), "SelectByCallID",
		func(_ dao_tool_call_task.ToolCallTaskBusiness, ctx context.Context, sessionID int64, callID string) (*dao_tool_call_task.ObjToolCallTask, error) {
			return createTestToolCallTask(2001, callID, sessionID, dao_tool_call_task.ToolCallTaskStatusRunning), nil
		})

	// Mock UpdateStatus
	patches.ApplyMethod(reflect.TypeOf(dao_tool_call_task.ToolCallTaskBusinessIns), "UpdateStatus",
		func(_ dao_tool_call_task.ToolCallTaskBusiness, ctx context.Context, taskID int64, status dao_tool_call_task.ToolCallTaskStatus, errorMsg string) error {
			assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusTimeout, status)
			assert.Equal(t, "任务执行超时", errorMsg)
			return nil
		})

	mockTimeForTesting(patches)
	mockLogger(patches)

	tc := &tool.ToolCallCommon{}
	resp, err := tc.WaitForTaskCompletion(ctx, 1001, "test-call-id", 30)

	assert.Error(t, err)
	assert.Nil(t, resp)

	if customErr, ok := err.(*lib_error.CustomErr); ok {
		assert.Equal(t, errcode.ToolCallTaskTimeoutError, customErr.Code)
		assert.Contains(t, customErr.Msg, "任务执行超时，超过30秒")
	}
}

// ===== WaitForTaskCompletion 任务状态错误处理测试 =====

func TestWaitForTaskCompletion_TaskStatus_Errors(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tests := []struct {
		name            string
		taskStatus      dao_tool_call_task.ToolCallTaskStatus
		expectedErrCode int
		expectedErrMsg  string
	}{
		{
			name:            "TaskStatus_Failed",
			taskStatus:      dao_tool_call_task.ToolCallTaskStatusFailed,
			expectedErrCode: errcode.ToolCallTaskTimeoutError,
			expectedErrMsg:  "failed: test error",
		},
		{
			name:            "TaskStatus_Timeout",
			taskStatus:      dao_tool_call_task.ToolCallTaskStatusTimeout,
			expectedErrCode: errcode.ToolCallTaskTimeoutError,
			expectedErrMsg:  "timeout: test error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyMethod(reflect.TypeOf(dao_tool_call_task.ToolCallTaskBusinessIns), "SelectByCallID",
				func(_ dao_tool_call_task.ToolCallTaskBusiness, ctx context.Context, sessionID int64, callID string) (*dao_tool_call_task.ObjToolCallTask, error) {
					task := createTestToolCallTask(2001, callID, sessionID, tt.taskStatus)
					task.ErrorMessage = "test error"
					return task, nil
				})

			mockTimeForTesting(patches)
			mockLogger(patches)

			tc := &tool.ToolCallCommon{}
			resp, err := tc.WaitForTaskCompletion(ctx, 1001, "test-call-id", 30)

			assert.Error(t, err)
			assert.NotNil(t, resp) // 失败状态仍返回响应
			assert.Equal(t, string(tt.taskStatus), resp.Status)

			if customErr, ok := err.(*lib_error.CustomErr); ok {
				assert.Equal(t, tt.expectedErrCode, customErr.Code)
				assert.Contains(t, customErr.Msg, tt.expectedErrMsg)
			}
		})
	}
}

// ===== GetTaskStatus 错误处理测试 =====

func TestGetTaskStatus_Error_Cases(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tests := []struct {
		name            string
		taskStatus      dao_tool_call_task.ToolCallTaskStatus
		expectedErrCode int
		expectedErrMsg  string
	}{
		{
			name:            "TaskStatus_Failed",
			taskStatus:      dao_tool_call_task.ToolCallTaskStatusFailed,
			expectedErrCode: errcode.ToolCallTaskExecuteError,
			expectedErrMsg:  "test error",
		},
		{
			name:            "TaskStatus_Timeout",
			taskStatus:      dao_tool_call_task.ToolCallTaskStatusTimeout,
			expectedErrCode: errcode.ToolCallTaskTimeoutError,
			expectedErrMsg:  "test error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.NewPatches()
			defer patches.Reset()

			patches.ApplyMethod(reflect.TypeOf(dao_tool_call_task.ToolCallTaskBusinessIns), "SelectByCallID",
				func(_ dao_tool_call_task.ToolCallTaskBusiness, ctx context.Context, sessionID int64, callID string) (*dao_tool_call_task.ObjToolCallTask, error) {
					task := createTestToolCallTask(2001, callID, sessionID, tt.taskStatus)
					task.ErrorMessage = "test error"
					return task, nil
				})

			mockLogger(patches)

			tc := &tool.ToolCallCommon{}
			resp, err := tc.GetTaskStatus(ctx, 1001, "test-call-id")

			assert.Error(t, err)
			assert.Nil(t, resp)

			if customErr, ok := err.(*lib_error.CustomErr); ok {
				assert.Equal(t, tt.expectedErrCode, customErr.Code)
				assert.Contains(t, customErr.Msg, tt.expectedErrMsg)
			}
		})
	}
}

// ===== ValidateToolCallRequest 参数验证测试 =====

func TestValidateToolCallRequest_Parameter_Validation(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	tests := []struct {
		name            string
		request         *tool.ToolCallRequest
		expectedErrCode int
		expectedErrMsg  string
		expectError     bool
	}{
		{
			name: "Valid_Request_AutoGenerateCallID",
			request: &tool.ToolCallRequest{
				SessionID: 1001,
				Name:      "test_tool",
				Arguments: `{"test": "value"}`,
				CallID:    "", // 空CallID，应该自动生成
			},
			expectError: false,
		},
		{
			name: "Missing_SessionID_And_SessionCode",
			request: &tool.ToolCallRequest{
				SessionID:   0,
				SessionCode: "",
				Name:        "test_tool",
				Arguments:   `{"test": "value"}`,
			},
			expectedErrCode: errcode.UserInputError,
			expectedErrMsg:  "session_id 和 session_code 必须提供一个",
			expectError:     true,
		},
		{
			name: "Missing_ToolName",
			request: &tool.ToolCallRequest{
				SessionID: 1001,
				Name:      "", // 空工具名
				Arguments: `{"test": "value"}`,
			},
			expectedErrCode: errcode.SQLSelectError,
			expectedErrMsg:  "tool name不能为空",
			expectError:     true,
		},
		{
			name: "Missing_Arguments",
			request: &tool.ToolCallRequest{
				SessionID: 1001,
				Name:      "test_tool",
				Arguments: "", // 空参数
			},
			expectedErrCode: errcode.SQLSelectError,
			expectedErrMsg:  "arguments不能为空",
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tc := &tool.ToolCallCommon{}
			err := tc.ValidateToolCallRequest(ctx, tt.request)

			if tt.expectError {
				assert.Error(t, err)
				if customErr, ok := err.(*lib_error.CustomErr); ok {
					assert.Equal(t, tt.expectedErrCode, customErr.Code)
					assert.Contains(t, customErr.Msg, tt.expectedErrMsg)
				}
			} else {
				assert.NoError(t, err)
				// 验证CallID自动生成
				if tt.request.CallID == "" {
					assert.NotEmpty(t, tt.request.CallID)
					assert.Len(t, tt.request.CallID, 36) // UUID长度
				}
			}
		})
	}
}

// ===== CreateToolCallTask 测试 =====

func TestCreateToolCallTask_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock Insert 方法
	patches.ApplyMethod(reflect.TypeOf(dao_tool_call_task.ToolCallTaskBusinessIns), "Insert",
		func(_ dao_tool_call_task.ToolCallTaskBusiness, ctx context.Context, task *dao_tool_call_task.ObjToolCallTask) (int64, error) {
			// 验证插入的任务数据
			assert.Equal(t, "test-call-id", task.CallID)
			assert.Equal(t, int64(1001), task.SessionID)
			assert.Equal(t, "test_tool", task.ToolName)
			assert.Equal(t, `{"test": "value"}`, task.Arguments)
			assert.Equal(t, dao_tool_call_task.ToolCallTaskStatusPending, task.Status)
			return 2001, nil
		})

	mockLogger(patches)

	tc := &tool.ToolCallCommon{}
	req := createTestToolCallRequest(1001, "")
	req.CallID = "test-call-id"
	session := createTestSessionForCommon(1001, "test_session", dao_session.ContainerStatusRunning)

	task, err := tc.CreateToolCallTask(ctx, req, session)

	assert.NoError(t, err)
	assert.NotNil(t, task)
	assert.Equal(t, int64(2001), task.TaskID)
	assert.Equal(t, "test-call-id", task.CallID)
	assert.Equal(t, int64(1001), task.SessionID)
}
