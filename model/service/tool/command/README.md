# 统一调用处理策略 (Unified Call Process Strategy)

## 概述

本目录实现了统一的工具调用处理策略模式，将原来分离的命令构建策略和响应组装策略合并为一个统一的接口。

## 架构设计

### 核心接口

- `CallProcessStrategy`: 统一的调用处理策略接口，包含命令构建和响应组装两个方法
- `CallProcessStrategyManager`: 策略管理器，负责注册和获取策略实例

### 策略实现

1. **MCPStrategy** (`mcp_strategy.go`)
   - 策略名称: "mcp"
   - 用于传统MCP执行路径
   - 命令构建: 简单实现，直接返回工具名和参数
   - 响应组装: 基于数据库任务状态的响应格式

2. **SweAgentStrategy** (`sweagent_strategy.go`)
   - 策略名称: "sweagent"
   - 用于K8s代理执行路径，遵循r2e项目标准
   - 命令构建: 将JSON参数转换为命令行参数格式
   - 响应组装: 格式化输出，支持bash工具特殊处理

## 文件结构

```
command/
├── call_process_strategy.go      # 核心接口定义
├── mcp_strategy.go              # MCP策略实现
├── sweagent_strategy.go         # SweAgent策略实现
├── call_process_strategy_test.go # 单元测试
└── README.md                    # 本文档
```

## 使用方式

### 全局管理器

```go
import "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/tool/command"

// 构建命令
cmdArgs, err := command.GlobalCallProcessStrategyManager.BuildCommand("sweagent", "file_editor", `{"command":"view","path":"/testbed"}`)

// 组装响应
response, err := command.GlobalCallProcessStrategyManager.AssembleResponse(ctx, "mcp", assemblyInput)
```

### 策略选择

- **MCP策略**: 当 `session.ImageID == ""` 时使用，走传统MCP执行路径
- **SweAgent策略**: 当 `session.ImageID != ""` 时使用，走K8s代理执行路径

## 数据结构

### 请求和响应

- `ToolCallRequest`: 工具调用请求结构
- `ToolCallResponse`: 工具调用响应结构
- `ResponseAssemblyInput`: 响应组装输入参数

### 命令构建

- `CommandInput`: 命令构建输入参数（预留扩展）

## 测试

运行测试：
```bash
go test ./command/
```

测试覆盖：
- 策略管理器基本功能
- MCP和SweAgent策略的命令构建
- MCP和SweAgent策略的响应组装
- 全局管理器初始化

## 迁移说明

本实现替代了原有的：
- `command_strategy.go` - 命令构建策略
- `response_assembly_strategy.go` - 响应组装策略

新的统一策略提供了更清晰的架构和更好的维护性。

## 扩展

要添加新的策略：

1. 实现 `CallProcessStrategy` 接口
2. 在 `NewCallProcessStrategyManager()` 中注册新策略
3. 添加相应的单元测试

## 注意事项

- 策略名称必须唯一
- 命令构建和响应组装必须在同一个策略中保持一致性
- 错误处理应该提供清晰的错误信息
- 响应格式应该遵循相应的标准（MCP或r2e）
