package command

import (
	"context"
	"encoding/json"
	"fmt"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
)

// MCPStrategy MCP策略实现（默认策略，保持原有MCP行为）
type MCPStrategy struct{}

// GetStrategyName 获取策略名称
func (s *MCPStrategy) GetStrategyName() string {
	return "mcp"
}

// BuildCommand 构建MCP命令（保持原有逻辑）
// MCP策略通常不需要特殊的命令构建，直接使用工具名称
func (s *MCPStrategy) BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error) {
	// MCP策略的命令构建逻辑（如果需要的话）
	// 目前保持简单，直接返回工具名称
	return []string{toolName}, nil
}

// AssembleResponse 组装MCP响应（保持原有数据库驱动的响应组装逻辑）
func (s *MCPStrategy) AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	if input.Task == nil {
		return nil, fmt.Errorf("MCP策略需要任务对象")
	}

	task := input.Task

	// 检查任务状态，如果失败或超时则返回错误
	switch task.Status {
	case dao_tool_call_task.ToolCallTaskStatusFailed:
		return nil, fmt.Errorf("任务执行失败: %s", task.ErrorMessage)
	case dao_tool_call_task.ToolCallTaskStatusTimeout:
		return nil, fmt.Errorf("任务执行超时: %s", task.ErrorMessage)
	case dao_tool_call_task.ToolCallTaskStatusSuccess:
		// 成功状态，继续处理
	default:
		return nil, fmt.Errorf("任务状态异常: %s", task.Status)
	}

	// 构建MCP格式的响应
	response := &ToolCallResponse{
		CallID:       input.CallID,
		Status:       string(task.Status),
		Result:       task.Result,
		OldEnvMD5:    task.OldEnvMd5,
		NewEnvMD5:    task.NewEnvMd5,
		OldEnvURL:    task.OldEnvUrl,
		NewEnvURL:    task.NewEnvUrl,
		ErrorMessage: task.ErrorMessage,
	}

	return response, nil
}

// parseResultJSON 解析结果JSON（MCP内部使用）
func (s *MCPStrategy) parseResultJSON(resultJSON string) (map[string]interface{}, error) {
	if resultJSON == "" {
		return make(map[string]interface{}), nil
	}

	var result map[string]interface{}
	if err := json.Unmarshal([]byte(resultJSON), &result); err != nil {
		return nil, fmt.Errorf("解析结果JSON失败: %w", err)
	}

	return result, nil
}
