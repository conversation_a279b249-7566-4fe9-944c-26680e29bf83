package command

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

func TestCallProcessStrategyManager(t *testing.T) {
	manager := NewCallProcessStrategyManager()
	ctx := context.Background()

	t.Run("has_mcp_strategy", func(t *testing.T) {
		strategy, err := manager.GetStrategy("mcp")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "mcp", strategy.GetStrategyName())
	})

	t.Run("has_sweagent_strategy", func(t *testing.T) {
		strategy, err := manager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "sweagent", strategy.GetStrategyName())
	})

	t.Run("unknown_strategy_returns_error", func(t *testing.T) {
		_, err := manager.GetStrategy("unknown")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "strategy 'unknown' not found")
	})

	t.Run("build_command_with_sweagent_strategy", func(t *testing.T) {
		command, err := manager.BuildCommand("sweagent", "file_editor", `{"command":"view","path":"/testbed","concise":true}`)
		assert.NoError(t, err)
		assert.Equal(t, []string{"file_editor", "view", "--path", "/testbed", "--concise", "True"}, command)
	})

	t.Run("assemble_response_with_mcp_strategy", func(t *testing.T) {
		input := &ResponseAssemblyInput{
			Request: &ToolCallRequest{
				CallID: "test_call_123",
				Name:   "test_tool",
			},
			Task: &dao_tool_call_task.ObjToolCallTask{
				Status:    dao_tool_call_task.ToolCallTaskStatusSuccess,
				Result:    `{"output": "test result"}`,
				OldEnvMd5: "old_md5",
				NewEnvMd5: "new_md5",
				OldEnvUrl: "old_url",
				NewEnvUrl: "new_url",
			},
			CallID:   "test_call_123",
			ToolName: "test_tool",
		}

		response, err := manager.AssembleResponse(ctx, "mcp", input)
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, "test_call_123", response.CallID)
		assert.Equal(t, "success", response.Status)
		assert.Equal(t, `{"output": "test result"}`, response.Result)
	})

	t.Run("assemble_response_with_sweagent_strategy", func(t *testing.T) {
		input := &ResponseAssemblyInput{
			Request: &ToolCallRequest{
				CallID: "bash_call_123",
				Name:   "execute_bash",
			},
			ExecResponse: &rpc_k8s_proxy.ExecCommandResponse{
				Code:    0,
				Stdout:  "Hello World",
				Stderr:  "",
				Message: "",
			},
			CallID:   "bash_call_123",
			ToolName: "execute_bash",
		}

		response, err := manager.AssembleResponse(ctx, "sweagent", input)
		assert.NoError(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, "bash_call_123", response.CallID)
		assert.Equal(t, "success", response.Status)
		assert.Contains(t, response.Result, "Exit code: 0")
		assert.Contains(t, response.Result, "Execution output of [execute_bash]:")
		assert.Contains(t, response.Result, "Hello World")
	})
}

func TestGlobalCallProcessStrategyManager(t *testing.T) {
	t.Run("global_manager_is_initialized", func(t *testing.T) {
		assert.NotNil(t, GlobalCallProcessStrategyManager)
		
		// 测试全局管理器包含预期的策略
		mcpStrategy, err := GlobalCallProcessStrategyManager.GetStrategy("mcp")
		assert.NoError(t, err)
		assert.NotNil(t, mcpStrategy)
		
		sweagentStrategy, err := GlobalCallProcessStrategyManager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, sweagentStrategy)
	})
}
