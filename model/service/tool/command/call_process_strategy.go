package command

import (
	"context"
	"encoding/json"
	"fmt"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

// ToolCallRequest 工具调用请求
type ToolCallRequest struct {
	SessionID   int64  `json:"session_id"`
	SessionCode string `json:"session_code"`
	CallID      string `json:"call_id"`
	Name        string `json:"name"`
	Arguments   string `json:"arguments"`
	TimeoutSecs int    `json:"timeout_secs"`
}

// ToolCallResponse 工具调用响应
type ToolCallResponse struct {
	CallID       string `json:"call_id"`
	Status       string `json:"status"`
	Result       string `json:"result"`
	ErrorMessage string `json:"error_message"`
	OldEnvMD5    string `json:"old_env_md5"`
	NewEnvMD5    string `json:"new_env_md5"`
	OldEnvURL    string `json:"old_env_url"`
	NewEnvURL    string `json:"new_env_url"`
}

// CommandInput 命令构建输入参数
type CommandInput struct {
	ToolName      string                 `json:"tool_name"`
	Arguments     map[string]interface{} `json:"arguments"`
	ArgumentsJSON string                 `json:"arguments_json"`
}

// ResponseAssemblyInput 响应组装输入参数
type ResponseAssemblyInput struct {
	Request      *ToolCallRequest                        // 工具调用请求
	Task         *dao_tool_call_task.ObjToolCallTask     // 任务对象（MCP路径使用）
	ExecResponse *rpc_k8s_proxy.ExecCommandResponse      // 执行响应（K8s代理路径使用）
	CallID       string                                  // 调用ID
	ToolName     string                                  // 工具名称
}

// CallProcessStrategy 调用处理策略接口（统一命令构建和响应组装）
type CallProcessStrategy interface {
	// GetStrategyName 获取策略名称
	GetStrategyName() string
	
	// BuildCommand 根据工具名称和参数构建执行命令
	BuildCommand(toolName string, arguments map[string]interface{}) ([]string, error)
	
	// AssembleResponse 组装工具调用响应
	AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error)
}

// CallProcessStrategyManager 调用处理策略管理器
type CallProcessStrategyManager struct {
	strategies map[string]CallProcessStrategy
}

// NewCallProcessStrategyManager 创建调用处理策略管理器
func NewCallProcessStrategyManager() *CallProcessStrategyManager {
	manager := &CallProcessStrategyManager{
		strategies: make(map[string]CallProcessStrategy),
	}

	// 注册默认策略
	manager.RegisterStrategy(&MCPStrategy{})
	manager.RegisterStrategy(&SweAgentStrategy{})

	return manager
}

// RegisterStrategy 注册策略
func (m *CallProcessStrategyManager) RegisterStrategy(strategy CallProcessStrategy) {
	m.strategies[strategy.GetStrategyName()] = strategy
}

// GetStrategy 获取策略
func (m *CallProcessStrategyManager) GetStrategy(strategyName string) (CallProcessStrategy, error) {
	strategy, exists := m.strategies[strategyName]
	if !exists {
		return nil, fmt.Errorf("strategy '%s' not found", strategyName)
	}
	return strategy, nil
}

// BuildCommand 使用指定策略构建命令
func (m *CallProcessStrategyManager) BuildCommand(strategyName, toolName string, argumentsJSON string) ([]string, error) {
	strategy, err := m.GetStrategy(strategyName)
	if err != nil {
		return nil, err
	}

	// 解析参数
	var arguments map[string]interface{}
	if argumentsJSON != "" && argumentsJSON != "{}" {
		if err := json.Unmarshal([]byte(argumentsJSON), &arguments); err != nil {
			return nil, fmt.Errorf("failed to parse arguments: %w", err)
		}
	}

	return strategy.BuildCommand(toolName, arguments)
}

// AssembleResponse 使用指定策略组装响应
func (m *CallProcessStrategyManager) AssembleResponse(ctx context.Context, strategyName string, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	strategy, err := m.GetStrategy(strategyName)
	if err != nil {
		return nil, err
	}

	return strategy.AssembleResponse(ctx, input)
}

// 全局策略管理器实例
var GlobalCallProcessStrategyManager = NewCallProcessStrategyManager()
