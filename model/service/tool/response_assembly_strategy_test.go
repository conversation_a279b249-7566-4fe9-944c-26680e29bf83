package tool

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

func TestMCPStrategy_AssembleResponse(t *testing.T) {
	strategy := &MCPResponseStrategy{}
	ctx := context.Background()

	tests := []struct {
		name     string
		input    *ResponseAssemblyInput
		expected *ToolCallResponse
		hasError bool
	}{
		{
			name: "successful task",
			input: &ResponseAssemblyInput{
				CallID:   "test_call_123",
				ToolName: "test_tool",
				Task: &dao_tool_call_task.ObjToolCallTask{
					Status:     dao_tool_call_task.ToolCallTaskStatusSuccess,
					Result:     `{"output": "test result"}`,
					OldEnvMd5:  "old_md5",
					NewEnvMd5:  "new_md5",
					OldEnvUrl:  "old_url",
					NewEnvUrl:  "new_url",
				},
			},
			expected: &ToolCallResponse{
				CallID:    "test_call_123",
				Status:    "success",
				Result:    `{"output": "test result"}`,
				OldEnvMD5: "old_md5",
				NewEnvMD5: "new_md5",
				OldEnvURL: "old_url",
				NewEnvURL: "new_url",
			},
			hasError: false,
		},
		{
			name: "failed task",
			input: &ResponseAssemblyInput{
				CallID:   "test_call_456",
				ToolName: "test_tool",
				Task: &dao_tool_call_task.ObjToolCallTask{
					Status:       dao_tool_call_task.ToolCallTaskStatusFailed,
					Result:       `{"error": "execution failed"}`,
					ErrorMessage: "Tool execution failed",
					OldEnvMd5:    "old_md5",
					NewEnvMd5:    "new_md5",
				},
			},
			expected: &ToolCallResponse{
				CallID:       "test_call_456",
				Status:       "failed",
				Result:       `{"error": "execution failed"}`,
				ErrorMessage: "Tool execution failed",
				OldEnvMD5:    "old_md5",
				NewEnvMD5:    "new_md5",
				OldEnvURL:    "",
				NewEnvURL:    "",
			},
			hasError: false,
		},
		{
			name: "missing task object",
			input: &ResponseAssemblyInput{
				CallID:   "test_call_789",
				ToolName: "test_tool",
				Task:     nil,
			},
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := strategy.AssembleResponse(ctx, tt.input)
			
			if tt.hasError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSweAgentStrategy_AssembleResponse(t *testing.T) {
	strategy := &SweAgentResponseStrategy{}
	ctx := context.Background()

	tests := []struct {
		name     string
		input    *ResponseAssemblyInput
		expected *ToolCallResponse
		hasError bool
	}{
		{
			name: "successful bash execution",
			input: &ResponseAssemblyInput{
				CallID:   "bash_call_123",
				ToolName: "execute_bash",
				ExecResponse: &rpc_k8s_proxy.ExecCommandResponse{
					Code:    0,
					Stdout:  "Hello World\nLine 2",
					Stderr:  "",
					Message: "",
				},
			},
			expected: &ToolCallResponse{
				CallID:       "bash_call_123",
				Status:       "success",
				Result:       "Exit code: 0\nExecution output of [execute_bash]:\nHello World\nLine 2",
				ErrorMessage: "",
				OldEnvMD5:    "",
				NewEnvMD5:    "",
				OldEnvURL:    "",
				NewEnvURL:    "",
			},
			hasError: false,
		},
		{
			name: "failed bash execution with stderr",
			input: &ResponseAssemblyInput{
				CallID:   "bash_call_456",
				ToolName: "bash",
				ExecResponse: &rpc_k8s_proxy.ExecCommandResponse{
					Code:    1,
					Stdout:  "Some output",
					Stderr:  "Error occurred\nSecond error line",
					Message: "Command failed",
				},
			},
			expected: &ToolCallResponse{
				CallID:       "bash_call_456",
				Status:       "failed",
				Result:       "Exit code: 1\nExecution output of [bash]:\nSome output\n🔴 Error occurred\n🔴 Second error line",
				ErrorMessage: "errcode: [1], errmsg: [Command failed], stdout: [Some output], stderr: [Error occurred\nSecond error line]",
				OldEnvMD5:    "",
				NewEnvMD5:    "",
				OldEnvURL:    "",
				NewEnvURL:    "",
			},
			hasError: false,
		},
		{
			name: "successful non-bash tool",
			input: &ResponseAssemblyInput{
				CallID:   "tool_call_789",
				ToolName: "str_replace_editor",
				ExecResponse: &rpc_k8s_proxy.ExecCommandResponse{
					Code:    0,
					Stdout:  "File content:\nline 1\nline 2",
					Stderr:  "",
					Message: "",
				},
			},
			expected: &ToolCallResponse{
				CallID:       "tool_call_789",
				Status:       "success",
				Result:       "Execution output of [str_replace_editor]:\nFile content:\nline 1\nline 2",
				ErrorMessage: "",
				OldEnvMD5:    "",
				NewEnvMD5:    "",
				OldEnvURL:    "",
				NewEnvURL:    "",
			},
			hasError: false,
		},
		{
			name: "no output",
			input: &ResponseAssemblyInput{
				CallID:   "empty_call_123",
				ToolName: "test_tool",
				ExecResponse: &rpc_k8s_proxy.ExecCommandResponse{
					Code:    0,
					Stdout:  "",
					Stderr:  "",
					Message: "",
				},
			},
			expected: &ToolCallResponse{
				CallID:       "empty_call_123",
				Status:       "success",
				Result:       "Execution output of [test_tool]:\n(no output)",
				ErrorMessage: "",
				OldEnvMD5:    "",
				NewEnvMD5:    "",
				OldEnvURL:    "",
				NewEnvURL:    "",
			},
			hasError: false,
		},
		{
			name: "missing exec response",
			input: &ResponseAssemblyInput{
				CallID:       "missing_exec_123",
				ToolName:     "test_tool",
				ExecResponse: nil,
			},
			expected: nil,
			hasError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := strategy.AssembleResponse(ctx, tt.input)
			
			if tt.hasError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestSweAgentStrategy_IsBashTool(t *testing.T) {
	strategy := &SweAgentResponseStrategy{}

	tests := []struct {
		toolName string
		expected bool
	}{
		{"execute_bash", true},
		{"bash", true},
		{"shell", true},
		{"str_replace_editor", false},
		{"file_editor", false},
		{"python", false},
	}

	for _, tt := range tests {
		t.Run(tt.toolName, func(t *testing.T) {
			result := strategy.isBashTool(tt.toolName)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSweAgentStrategy_MaybeTruncate(t *testing.T) {
	strategy := &SweAgentResponseStrategy{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "short content",
			input:    "Short content",
			expected: "Short content",
		},
		{
			name:     "long content gets truncated",
			input:    string(make([]byte, 15000)), // 15k bytes
			expected: string(make([]byte, 10000)) + "\n\n<response clipped><NOTE>To save on context only part of this output has been shown to you. The output was truncated because it was too long.</NOTE>",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := strategy.maybeTruncate(tt.input)
			if len(tt.input) <= 10000 {
				assert.Equal(t, tt.expected, result)
			} else {
				assert.Contains(t, result, "<response clipped>")
				assert.True(t, len(result) < len(tt.input))
			}
		})
	}
}

func TestResponseAssemblyStrategyManager(t *testing.T) {
	manager := NewResponseAssemblyStrategyManager()

	t.Run("has mcp strategy", func(t *testing.T) {
		strategy, err := manager.GetStrategy("mcp")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "mcp", strategy.GetStrategyName())
	})

	t.Run("has sweagent strategy", func(t *testing.T) {
		strategy, err := manager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, strategy)
		assert.Equal(t, "sweagent", strategy.GetStrategyName())
	})

	t.Run("unknown strategy returns error", func(t *testing.T) {
		strategy, err := manager.GetStrategy("unknown")
		assert.Error(t, err)
		assert.Nil(t, strategy)
		assert.Contains(t, err.Error(), "not found")
	})

	t.Run("assemble response with mcp strategy", func(t *testing.T) {
		input := &ResponseAssemblyInput{
			CallID:   "test_call",
			ToolName: "test_tool",
			Task: &dao_tool_call_task.ObjToolCallTask{
				Status: dao_tool_call_task.ToolCallTaskStatusSuccess,
				Result: "test result",
			},
		}

		result, err := manager.AssembleResponse(context.Background(), "mcp", input)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test_call", result.CallID)
		assert.Equal(t, "success", result.Status)
	})
}

func TestGlobalResponseAssemblyStrategyManager(t *testing.T) {
	t.Run("global manager has both strategies", func(t *testing.T) {
		mcpStrategy, err := GlobalResponseAssemblyStrategyManager.GetStrategy("mcp")
		assert.NoError(t, err)
		assert.NotNil(t, mcpStrategy)

		sweagentStrategy, err := GlobalResponseAssemblyStrategyManager.GetStrategy("sweagent")
		assert.NoError(t, err)
		assert.NotNil(t, sweagentStrategy)
	})
}
