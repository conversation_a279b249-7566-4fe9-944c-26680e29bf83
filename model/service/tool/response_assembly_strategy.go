package tool

import (
	"context"
	"fmt"
	"strings"

	dao_tool_call_task "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/tool_call_task"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
)

// ResponseAssemblyStrategy 响应组装策略接口
type ResponseAssemblyStrategy interface {
	// AssembleResponse 根据输入数据组装工具调用响应
	AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error)
	// GetStrategyName 获取策略名称
	GetStrategyName() string
}

// ResponseAssemblyInput 响应组装输入数据
type ResponseAssemblyInput struct {
	Request      *ToolCallRequest                      // 原始请求
	ExecResponse *rpc_k8s_proxy.ExecCommandResponse    // K8s代理执行响应（K8s路径使用）
	Task         *dao_tool_call_task.ObjToolCallTask   // 任务对象（MCP路径使用）
	CallID       string                                // 调用ID
	ToolName     string                                // 工具名称
}

// ResponseAssemblyStrategyManager 响应组装策略管理器
type ResponseAssemblyStrategyManager struct {
	strategies map[string]ResponseAssemblyStrategy
}

// NewResponseAssemblyStrategyManager 创建响应组装策略管理器
func NewResponseAssemblyStrategyManager() *ResponseAssemblyStrategyManager {
	manager := &ResponseAssemblyStrategyManager{
		strategies: make(map[string]ResponseAssemblyStrategy),
	}

	// 注册默认策略
	manager.RegisterStrategy(&MCPResponseStrategy{})
	manager.RegisterStrategy(&SweAgentResponseStrategy{})

	return manager
}

// RegisterStrategy 注册策略
func (m *ResponseAssemblyStrategyManager) RegisterStrategy(strategy ResponseAssemblyStrategy) {
	m.strategies[strategy.GetStrategyName()] = strategy
}

// GetStrategy 获取策略
func (m *ResponseAssemblyStrategyManager) GetStrategy(strategyName string) (ResponseAssemblyStrategy, error) {
	strategy, exists := m.strategies[strategyName]
	if !exists {
		return nil, fmt.Errorf("response assembly strategy '%s' not found", strategyName)
	}
	return strategy, nil
}

// AssembleResponse 使用指定策略组装响应
func (m *ResponseAssemblyStrategyManager) AssembleResponse(ctx context.Context, strategyName string, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	strategy, err := m.GetStrategy(strategyName)
	if err != nil {
		return nil, err
	}

	return strategy.AssembleResponse(ctx, input)
}

// MCPResponseStrategy MCP策略实现（默认策略，保持原有MCP行为）
type MCPResponseStrategy struct{}

// GetStrategyName 获取策略名称
func (s *MCPResponseStrategy) GetStrategyName() string {
	return "mcp"
}

// AssembleResponse 组装MCP响应
func (s *MCPResponseStrategy) AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	if input.Task == nil {
		return nil, fmt.Errorf("MCP strategy requires task object")
	}

	task := input.Task

	// 根据任务状态组装响应
	switch task.Status {
	case dao_tool_call_task.ToolCallTaskStatusSuccess:
		return &ToolCallResponse{
			CallID:    input.CallID,
			Status:    string(task.Status),
			Result:    task.Result,
			OldEnvMD5: task.OldEnvMd5,
			NewEnvMD5: task.NewEnvMd5,
			OldEnvURL: task.OldEnvUrl,
			NewEnvURL: task.NewEnvUrl,
		}, nil

	case dao_tool_call_task.ToolCallTaskStatusFailed, dao_tool_call_task.ToolCallTaskStatusTimeout:
		return &ToolCallResponse{
			CallID:       input.CallID,
			Status:       string(task.Status),
			Result:       task.Result,
			ErrorMessage: task.ErrorMessage,
			OldEnvMD5:    task.OldEnvMd5,
			NewEnvMD5:    task.NewEnvMd5,
			OldEnvURL:    task.OldEnvUrl,
			NewEnvURL:    task.NewEnvUrl,
		}, nil

	default:
		return &ToolCallResponse{
			CallID:       input.CallID,
			Status:       string(task.Status),
			Result:       task.Result,
			OldEnvMD5:    task.OldEnvMd5,
			NewEnvMD5:    task.NewEnvMd5,
			OldEnvURL:    task.OldEnvUrl,
			NewEnvURL:    task.NewEnvUrl,
			ErrorMessage: task.ErrorMessage,
		}, nil
	}
}

// SweAgentResponseStrategy SweAgent策略实现（遵循r2e项目标准）
type SweAgentResponseStrategy struct{}

// GetStrategyName 获取策略名称
func (s *SweAgentResponseStrategy) GetStrategyName() string {
	return "sweagent"
}

// AssembleResponse 组装SweAgent响应
func (s *SweAgentResponseStrategy) AssembleResponse(ctx context.Context, input *ResponseAssemblyInput) (*ToolCallResponse, error) {
	if input.ExecResponse == nil {
		return nil, fmt.Errorf("SweAgent strategy requires exec response")
	}

	execResp := input.ExecResponse
	toolName := input.ToolName

	// 根据r2e标准格式化响应内容
	var resultContent string
	
	// 判断是否为bash类工具
	isBashTool := s.isBashTool(toolName)
	
	if isBashTool {
		// Bash工具格式: "Exit code: {code}\nExecution output of [{tool_name}]:\n{output}"
		resultContent = fmt.Sprintf("Exit code: %d\nExecution output of [%s]:\n%s", 
			execResp.Code, toolName, s.formatBashOutput(execResp))
	} else {
		// 其他工具格式: "Execution output of [{tool_name}]:\n{output}"
		resultContent = fmt.Sprintf("Execution output of [%s]:\n%s", 
			toolName, s.formatToolOutput(execResp))
	}

	// 应用截断策略
	resultContent = s.maybeTruncate(resultContent)

	// 构建错误消息（用于内部记录）
	var errorMessage string
	if execResp.Code != 0 || execResp.Stderr != "" {
		errorMessage = fmt.Sprintf("errcode: [%d], errmsg: [%s], stdout: [%s], stderr: [%s]",
			execResp.Code, execResp.Message, execResp.Stdout, execResp.Stderr)
		
		// 限制错误消息长度
		const maxErrorMessageLength = 65000
		if len(errorMessage) > maxErrorMessageLength {
			errorMessage = errorMessage[:maxErrorMessageLength] + "...[truncated]"
		}
	}

	// 确定状态
	status := "success"
	if execResp.Code != 0 {
		status = "failed"
	}

	return &ToolCallResponse{
		CallID:       input.CallID,
		Status:       status,
		Result:       resultContent,
		ErrorMessage: errorMessage,
		OldEnvMD5:    "", // SweAgent策略不使用环境MD5
		NewEnvMD5:    "",
		OldEnvURL:    "",
		NewEnvURL:    "",
	}, nil
}

// isBashTool 判断是否为bash类工具
func (s *SweAgentResponseStrategy) isBashTool(toolName string) bool {
	bashTools := []string{"execute_bash", "bash", "shell"}
	for _, bashTool := range bashTools {
		if toolName == bashTool {
			return true
		}
	}
	return false
}

// formatBashOutput 格式化bash输出
func (s *SweAgentResponseStrategy) formatBashOutput(execResp *rpc_k8s_proxy.ExecCommandResponse) string {
	if execResp.Stdout == "" && execResp.Stderr == "" {
		return "(no output)"
	}

	var output strings.Builder
	
	// 添加标准输出
	if execResp.Stdout != "" {
		output.WriteString(execResp.Stdout)
	}
	
	// 添加标准错误（用🔴标记）
	if execResp.Stderr != "" {
		if output.Len() > 0 {
			output.WriteString("\n")
		}
		// 为每行stderr添加🔴前缀
		stderrLines := strings.Split(execResp.Stderr, "\n")
		for i, line := range stderrLines {
			if line != "" {
				if i > 0 {
					output.WriteString("\n")
				}
				output.WriteString("🔴 ")
				output.WriteString(line)
			}
		}
	}
	
	return output.String()
}

// formatToolOutput 格式化工具输出
func (s *SweAgentResponseStrategy) formatToolOutput(execResp *rpc_k8s_proxy.ExecCommandResponse) string {
	if execResp.Stdout == "" && execResp.Stderr == "" {
		return "(no output)"
	}
	
	var output strings.Builder
	
	// 添加标准输出
	if execResp.Stdout != "" {
		output.WriteString(execResp.Stdout)
	}
	
	// 如果有错误，添加到输出中
	if execResp.Stderr != "" {
		if output.Len() > 0 {
			output.WriteString("\n")
		}
		output.WriteString("Error: ")
		output.WriteString(execResp.Stderr)
	}
	
	return output.String()
}

// maybeTruncate 应用截断策略
func (s *SweAgentResponseStrategy) maybeTruncate(content string) string {
	const maxResponseLen = 10000 // 最大字符数
	
	if len(content) <= maxResponseLen {
		return content
	}
	
	// UTF-8安全截断
	truncated := content[:maxResponseLen]
	for len(truncated) > 0 && truncated[len(truncated)-1] >= 0x80 {
		truncated = truncated[:len(truncated)-1]
	}
	
	truncatedMessage := "\n\n<response clipped><NOTE>To save on context only part of this output has been shown to you. The output was truncated because it was too long.</NOTE>"
	return truncated + truncatedMessage
}

// 全局响应组装策略管理器实例
var GlobalResponseAssemblyStrategyManager = NewResponseAssemblyStrategyManager()
