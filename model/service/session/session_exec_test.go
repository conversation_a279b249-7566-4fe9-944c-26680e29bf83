package session_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session"
)

func TestSessionExec_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_exec_001",
		SessionCode:     fmt.Sprintf("test_session_exec_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusRunning,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 使用gomonkey模拟K8s客户端的ExecCommand方法
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	expectedOutput := "command executed successfully"
	patches.ApplyMethod(&rpc_k8s_proxy.K8sProxyClient{}, "ExecCommand",
		func(_ *rpc_k8s_proxy.K8sProxyClient, ctx context.Context, req *rpc_k8s_proxy.ExecCommandRequest) (*rpc_k8s_proxy.ExecCommandResponse, error) {
			// 验证请求参数
			assert.Equal(t, "mcp", req.SourceType)
			assert.Equal(t, "job_test_session_exec_001", req.JobName)
			assert.Equal(t, []string{"echo", "hello"}, req.Command)

			return &rpc_k8s_proxy.ExecCommandResponse{
				Stdout: expectedOutput,
			}, nil
		})

	// 执行命令执行操作
	execService := &service.SessionExec{}
	execService.InputData = &service.SessionExecInputData{
		SessionID: sessionID,
		Command:   "echo hello",
	}

	resp, _, err := execService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	output, ok := resp.(*service.SessionExecOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, expectedOutput, output.Stdout)
}

func TestSessionExec_Execute_SessionNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建服务实例（使用不存在的SessionID）
	execService := &service.SessionExec{}
	execService.InputData = &service.SessionExecInputData{
		SessionID: 99999, // 不存在的Session ID
		Command:   "echo hello",
	}

	// 执行测试
	resp, _, err := execService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	customErr, ok := err.(*lib_error.CustomErr)
	assert.True(t, ok)
	assert.Equal(t, errcode.SessionNotFound, customErr.Code)
}

func TestSessionExec_Execute_SessionNotRunning(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session（状态为停止）
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	session := &dao_session.ObjSession{
		JobID:           "job_test_session_exec_stopped",
		SessionCode:     fmt.Sprintf("test_session_exec_stopped_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		ContainerStatus: dao_session.ContainerStatusStopped,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 创建服务实例
	execService := &service.SessionExec{}
	execService.InputData = &service.SessionExecInputData{
		SessionID: sessionID,
		Command:   "echo hello",
	}

	// 执行测试
	resp, _, err := execService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	customErr, ok := err.(*lib_error.CustomErr)
	assert.True(t, ok)
	assert.Equal(t, errcode.SessionFailed, customErr.Code)
}

func TestSessionExec_Execute_InvalidInput(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建服务实例（没有提供SessionID或SessionCode）
	execService := &service.SessionExec{}
	execService.InputData = &service.SessionExecInputData{
		Command: "echo hello",
	}

	// 执行测试
	resp, _, err := execService.Execute(ctx, nil)

	// 验证结果
	assert.Error(t, err)
	assert.Nil(t, resp)

	// 验证错误类型
	customErr, ok := err.(*lib_error.CustomErr)
	assert.True(t, ok)
	assert.Equal(t, errcode.InvalidInput, customErr.Code)
}
