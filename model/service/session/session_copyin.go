package session

import (
	"context"
	"fmt"
	"log"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// FileInfo 文件信息
type FileInfo struct {
	FileURL  string `json:"file_url" validate:"required"`
	DestPath string `json:"dest_path" validate:"required"`
}

// SessionCopyinInputData Session文件复制输入数据
type SessionCopyinInputData struct {
	SessionID   int64      `json:"session_id,omitempty"`
	SessionCode string     `json:"session_code,omitempty"`
	Files       []FileInfo `json:"files" validate:"required,min=1"`
}

// SessionCopyinOutputData Session文件复制输出数据
type SessionCopyinOutputData struct {
	SessionID int64  `json:"session_id"`
	Success   bool   `json:"success"`
	Message   string `json:"message,omitempty"`
}

// SessionCopyin Session文件复制服务
type SessionCopyin struct {
	base.Service[SessionCopyinInputData, SessionCopyinOutputData]
}

// Execute 执行Session文件复制逻辑
func (s *SessionCopyin) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	var session *dao_session.ObjSession

	// 1. 根据输入参数获取Session
	if input.SessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, input.SessionCode)
		if err != nil {
			log.Printf("通过SessionCode查询Session失败: %v", err)
			return nil, nil, err
		}
	} else if input.SessionID != 0 {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
		if err != nil {
			log.Printf("通过SessionID查询Session失败: %v", err)
			return nil, nil, err
		}
	} else {
		return nil, nil, &lib_error.CustomErr{Code: errcode.InvalidInput, Msg: "session_id或session_code必须提供一个"}
	}

	sessionID := session.SessionID

	// 2. 检查Session状态
	if session.ContainerStatus != dao_session.ContainerStatusRunning {
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.SessionFailed,
			Msg:  fmt.Sprintf("Session状态不是运行中，当前状态: %s", session.ContainerStatus),
		}
	}

	// 3. 检查Session是否有关联的容器任务
	if session.JobID == "" {
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.SessionNotFound,
			Msg:  "Session没有关联的容器任务",
		}
	}

	// 4. 转换文件信息格式
	var fileInfos []rpc_k8s_proxy.FileInfo
	for _, file := range input.Files {
		fileInfos = append(fileInfos, rpc_k8s_proxy.FileInfo{
			FileURL:  file.FileURL,
			DestPath: file.DestPath,
		})
	}

	// 5. 调用k8s_proxy上传文件
	uploadReq := &rpc_k8s_proxy.UploadFileRequest{
		SourceType: "mcp",
		JobName:    session.JobID,
		FileInfo:   fileInfos,
	}

	uploadResp, err := rpc_k8s_proxy.K8sProxyClientIns.UploadFile(ctx, uploadReq)
	if err != nil {
		log.Printf("上传文件失败: %v", err)
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.K8sContainerCreateError,
			Msg:  fmt.Sprintf("上传文件失败: %v", err),
		}
	}

	// 6. 返回上传结果
	output := &SessionCopyinOutputData{
		SessionID: sessionID,
		Success:   uploadResp.Success,
	}

	if uploadResp.Success {
		output.Message = fmt.Sprintf("成功上传 %d 个文件", len(input.Files))
	} else {
		output.Message = "文件上传失败"
		log.Printf("Session文件复制失败: session_id=%d, files_count=%d", sessionID, len(input.Files))
	}

	return output, nil, nil
}
