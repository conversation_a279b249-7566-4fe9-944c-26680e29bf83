package session

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"
	rcc "icode.baidu.com/baidu/smartprogram/rcc2-go-sdk"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/config"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_mcp_env "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/mcp_env"
	dao_obj_image "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/obj_image"
	dao_register_mcp_server "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/register_mcp_server"
	rpc_bos "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_bos"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/model/environment"
)

// SessionInitInputData Session初始化输入数据
type SessionInitInputData struct {
	ServerIDs       []int64 `json:"server_ids" validate:"omitempty,min=1"`
	EnvID           int64   `json:"env_id" validate:"omitempty"`
	ImageID         string  `json:"image_id,omitempty"`
	TimeoutMinutes  int     `json:"timeout_minutes,omitempty"`
	SessionCode     string  `json:"session_code,omitempty"`
	UseServerPrefix string  `json:"use_server_prefix,omitempty"`
}

// SessionInitOutputData Session初始化输出数据
type SessionInitOutputData struct {
	SessionID   int64           `json:"session_id"`
	SessionCode string          `json:"session_code"`
	TaskID      string          `json:"task_id"`
	MCPTools    []types.MCPTool `json:"mcp_tools"`
}

// SessionInit Session初始化服务
type SessionInit struct {
	base.Service[SessionInitInputData, SessionInitOutputData]
}

// Execute 执行Session初始化逻辑
func (s *SessionInit) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	var sessionID int64

	if input.TimeoutMinutes == 0 {
		input.TimeoutMinutes = 30
	}
	if input.ImageID == "" && len(input.ServerIDs) == 0 {
		return nil, nil, &lib_error.CustomErr{Code: errcode.InvalidInput, Msg: "image_id和server_ids不能同时为空"}
	}

	// 1. 验证服务器存在（仅当ServerIDs不为空时）
	if len(input.ServerIDs) > 0 {
		serversMap, err := dao_register_mcp_server.RegisterMcpServerBusinessIns.GetMapByPrimaryKeys(ctx, input.ServerIDs)
		if err != nil {
			log.Printf("查询MCP服务器失败: %v", err)
			return nil, nil, err
		}

		// 检查是否所有服务器都存在
		for _, serverID := range input.ServerIDs {
			if _, exists := serversMap[serverID]; !exists {
				errMsg := fmt.Sprintf("MCP服务器不存在: server_id=%d", serverID)
				log.Println(errMsg)
				return nil, nil, &lib_error.CustomErr{Code: errcode.McpServerNotFound, Msg: errMsg}
			}
		}
	}

	// 2. 验证环境存在（仅当EnvID大于0时）
	var env *dao_mcp_env.ObjMcpEnv
	if input.EnvID > 0 {
		env, err = dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, input.EnvID)
		if err != nil {
			log.Printf("查询MCP环境失败: %v", err)
			return nil, nil, err
		}
		if env == nil {
			errMsg := fmt.Sprintf("MCP环境不存在: env_id=%d", input.EnvID)
			log.Println(errMsg)
			return nil, nil, &lib_error.CustomErr{Code: errcode.SQLSelectError, Msg: errMsg}
		}
	}

	// 3. 验证镜像存在（如果指定了镜像ID）
	var imageConfig *dao_obj_image.ObjImage
	if input.ImageID != "" {
		imageConfig, err = dao_obj_image.ObjImageBusinessIns.SelectByImageID(ctx, input.ImageID)
		if err != nil {
			log.Printf("查询镜像失败: %v", err)
			return nil, nil, err
		}
	}

	// 兼容session_code 为空情况
	if input.SessionCode == "" {
		input.SessionCode = uuid.New().String()
	}

	// 4. 创建Session记录
	serverIDs := dao_base.JSONArray[int64](s.InputData.ServerIDs)
	newSession := &dao_session.ObjSession{
		EnvID:           input.EnvID,
		ImageID:         input.ImageID,
		ServerIds:       serverIDs,
		SessionCode:     input.SessionCode,
		McpTools:        nil, // 初始化时为空，等待容器上报
		ContainerStatus: dao_session.ContainerStatusInit,
		TimeoutSeconds:  int64(input.TimeoutMinutes * 60),
	}

	sessionID, err = dao_session.SessionBusinessIns.Insert(ctx, newSession)
	if err != nil {
		log.Printf("创建Session记录失败: %v", err)
		return nil, nil, err
	}

	// 5. 构建容器初始化配置
	containerReq, err := s.buildContainerRequest(sessionID, imageConfig)
	if err != nil {
		// 更新session状态为失败并记录错误
		errMsg := fmt.Sprintf("构建容器请求失败: %v", err)
		_ = dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, sessionID, dao_session.ContainerStatusFailed, errMsg)
		return nil, nil, &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: errMsg}
	}

	// 5. 调用容器管理平台创建容器
	// 在调用CreateTask前增加随机等待，避免大量并发请求同时到达
	time.Sleep(time.Duration(rand.Intn(5000)) * time.Millisecond)
	containerResp, err := rpc_k8s_proxy.K8sProxyClientIns.CreateTask(ctx, containerReq)
	if err != nil {
		// 更新session状态为失败并记录错误
		errMsg := fmt.Sprintf("创建容器失败: %v", err)
		_ = dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, sessionID, dao_session.ContainerStatusFailed, errMsg)
		if customErr, ok := err.(*lib_error.CustomErr); ok {
			return nil, nil, customErr
		}
		return nil, nil, &lib_error.CustomErr{Code: errcode.K8sContainerCreateError, Msg: errMsg}
	}

	// 数据库更新session状态为pending
	err = dao_session.SessionBusinessIns.UpdateJobIDAndStatus(ctx, sessionID, containerResp.TaskID, dao_session.ContainerStatusPending)
	if err != nil {
		log.Printf("更新session状态失败: %v", err)
		return nil, nil, err
	}

	// 6. 轮询等待容器就绪
	mcpTools, err := s.waitForContainerReady(ctx, sessionID, containerResp.TaskID, imageConfig)
	if err != nil {
		// 更新session状态为失败并记录错误
		errMsg := fmt.Sprintf("等待容器就绪失败: %v", err)
		_ = dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, sessionID, dao_session.ContainerStatusFailed, errMsg)
		return nil, nil, &lib_error.CustomErr{Code: errcode.K8sContainerCreateError, Msg: errMsg}
	}

	// 7. 返回成功结果
	output := &SessionInitOutputData{
		SessionID:   sessionID,
		SessionCode: input.SessionCode,
		TaskID:      containerResp.TaskID,
		MCPTools:    mcpTools,
	}

	return output, nil, nil
}

// buildContainerRequest 构建容器创建请求
func (s *SessionInit) buildContainerRequest(sessionID int64, imageConfig *dao_obj_image.ObjImage) (*rpc_k8s_proxy.CreateTaskRequest, error) {
	// 构建基础参数数组，这些参数通常需要保留
	args := []string{
		"--session-id", strconv.FormatInt(sessionID, 10),
		"--mcp-api-base", rcc.GetValue("k8s_proxy.mcp_api_base", "http://127.0.0.1:8080"),
		// BOS配置参数 - 使用全局配置
		"--timeout", (time.Duration(s.InputData.TimeoutMinutes) * time.Minute).String(),
		"--bos-access-key", config.DataStorageBosConfigGlobal.AccessKey,
		"--bos-secret-key", config.DataStorageBosConfigGlobal.SecretKey,
		"--bos-endpoint", config.DataStorageBosConfigGlobal.Endpoint,
		"--bos-bucket", config.DataStorageBosConfigGlobal.Bucket,
	}

	if s.InputData.UseServerPrefix == "" || strings.ToLower(s.InputData.UseServerPrefix) == "true" {
		args = append(args, "--use-server-prefix")
	}

	// 如果imageConfig存在且ContainerArgs不为空，则使用imageConfig中的args，否则使用默认构建的args
	var finalArgs []string
	if imageConfig != nil && len(imageConfig.ContainerArgs) > 0 {
		// finalArgs = append(args, imageConfig.ContainerArgs...)
		finalArgs = imageConfig.ContainerArgs
	} else {
		finalArgs = args
	}

	// 确定镜像路径：优先使用imageConfig中的ImagePath，否则使用默认拼接
	var imagePath string
	if imageConfig != nil && imageConfig.ImagePath != "" {
		imagePath = imageConfig.ImagePath
	} else {
		imagePath = rcc.GetValue("k8s_proxy.image_path", "mcp/mcp-runtime:latest")
	}
	imagePath = rcc.GetValue("k8s_proxy.image_registry", "ccr-2y3xupwh-vpc.cnc.bj.baidubce.com") + "/" + imagePath

	// 确定容器命令：优先使用imageConfig中的ContainerCommand，否则使用默认命令
	var command string
	if imageConfig != nil && imageConfig.ContainerCommand != "" {
		command = imageConfig.ContainerCommand
	} else {
		command = "./mcp_runtime"
	}

	// 确定资源配置：优先使用imageConfig中的ContainerResources，否则使用默认配置
	var resources string
	if imageConfig != nil && imageConfig.ContainerResources != nil {
		// 将JSONData转换为字符串
		resourcesBytes, err := json.Marshal(imageConfig.ContainerResources)
		if err != nil {
			log.Printf("序列化容器资源配置失败: %v", err)
			resources = rcc.GetValue("k8s_proxy.init.resources", "")
		} else {
			resources = string(resourcesBytes)
		}
	} else {
		resources = rcc.GetValue("k8s_proxy.init.resources", "")
	}

	isRootUser := false
	if imageConfig != nil {
		isRootUser = imageConfig.RootUser
	}
	containerReq := &rpc_k8s_proxy.CreateTaskRequest{
		SessionID:  sessionID,
		ImagePath:  imagePath,
		Command:    command,
		Args:       finalArgs,
		Timeout:    s.InputData.TimeoutMinutes*60 + 300,
		Input:      "[]",
		OutputInfo: "{}",
		SourceType: rcc.GetValue("k8s_proxy.init.source_type", "mcp"),
		TaskType:   rcc.GetValue("k8s_proxy.init.task_type", "mcp"),
		Resources:  resources,
		IsRootUser: isRootUser,
	}

	return containerReq, nil
}

// waitForContainerReady 等待容器就绪并获取MCP工具列表
func (s *SessionInit) waitForContainerReady(ctx context.Context, sessionID int64, taskID string, imageConfig *dao_obj_image.ObjImage) ([]types.MCPTool, error) {
	pollingInterval := 5 * time.Second
	maxWaitTime := time.Duration(120) * time.Second
	startTime := time.Now()
	// 等待一段时间后再去轮询状态，避免容器刚创建数据不一致
	time.Sleep(pollingInterval)
	var lastState string
	consecutiveErrors := 0

	for {
		// 检查是否超时
		if time.Since(startTime) > maxWaitTime {
			return nil, &lib_error.CustomErr{Code: errcode.K8sContainerStartError, Msg: fmt.Sprintf("容器启动超时: 超过%v", maxWaitTime)}
		}

		// 查询容器状态
		taskState, err := rpc_k8s_proxy.K8sProxyClientIns.GetTaskState(ctx, taskID)
		if err != nil {
			consecutiveErrors++
			log.Printf("查询容器状态失败: %v (连续失败%d次)", err, consecutiveErrors)
			// 如果是严重错误(如context deadline exceeded)或连续失败超过3次，获取日志并返回错误
			if strings.Contains(err.Error(), "context deadline exceeded") ||
				strings.Contains(err.Error(), "context has error already") ||
				consecutiveErrors >= 10 {
				log.Printf("检测到严重错误或连续失败，获取容器日志...")

				// 获取容器日志
				logResp, logErr := rpc_k8s_proxy.K8sProxyClientIns.GetTaskLogStream(ctx, taskID)
				var logMsg string
				if logErr != nil {
					logMsg = fmt.Sprintf("获取容器日志失败: %v", logErr)
				} else {
					logMsg = fmt.Sprintf("容器日志: %s", logResp.Log)
				}

				return nil, &lib_error.CustomErr{
					Code: errcode.K8sContainerStartError,
					Msg:  fmt.Sprintf("容器状态查询失败: %v, %s", err, logMsg),
				}
			}
			continue
		}

		// 重置连续错误计数
		consecutiveErrors = 0
		currentState := taskState.State

		switch taskState.State {
		case "running":
			//为空说明使用mcp容器
			if imageConfig == nil {
				// 容器正在运行，检查数据库中是否已更新工具列表
				session, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
				if err != nil {
					log.Printf("查询Session状态失败: %v", err)
					time.Sleep(pollingInterval)
					continue
				}

				// 如果容器状态为running且工具列表不为空，说明容器已就绪
				if session.ContainerStatus == "running" && session.McpTools != nil {
					mcpTools, err := utils.ConvertJSONDataToMCPTools(&session.McpTools)
					if err != nil {
						return nil, &lib_error.CustomErr{Code: errcode.SysFileReadError, Msg: fmt.Sprintf("解析MCP工具列表失败: %v", err)}
					}
					return mcpTools, nil
				}
			} else {
				// 如果传入了imageConfig，执行环境初始化
				err := s.initializeContainerEnvironment(ctx, sessionID, taskID, imageConfig)
				if err != nil {
					log.Printf("容器环境初始化失败: %v", err)
					// 更新session状态为失败并记录错误
					errMsg := fmt.Sprintf("创建容器失败: %v", err)
					err = &lib_error.CustomErr{Code: errcode.K8sContainerStartError, Msg: fmt.Sprintf("容器环境初始化失败: %v", err)}
					_ = dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, sessionID, dao_session.ContainerStatusFailed, errMsg)
					//TODO暂时保留job容器方便排查
					// if err := rpc_k8s_proxy.K8sProxyClientIns.DeleteJob(ctx, taskID); err != nil {
					// 	log.Printf("删除容器失败: %v", err)
					// }
					return nil, err
				}
				//更新session状态为running
				_ = dao_session.SessionBusinessIns.UpdateJobIDAndStatus(ctx, sessionID, taskID, dao_session.ContainerStatusRunning)
				return nil, nil
			}

		case "success", "failed":
			// 获取容器日志
			logMsg := taskState.Log
			if logMsg == "" {
				logMsg = "GetTaskState返回的容器日志为空"
			}
			return nil, &lib_error.CustomErr{
				Code: errcode.K8sContainerStartError,
				Msg:  "容器启动失败,状态：" + taskState.State + ", 失败原因：" + logMsg}
		case "pending":
			// 容器还在启动中，继续等待
		case "deleted":
			return nil, &lib_error.CustomErr{Code: errcode.K8sContainerStartError, Msg: "容器被删除"}
		default:
			// 对于未知状态，只在状态变化时打印警告
			if currentState != lastState {
				log.Printf("检测到未知容器状态: %s", taskState.State)
			}
		}
		time.Sleep(pollingInterval)
	}
}

// initializeContainerEnvironment 在容器内初始化环境
func (s *SessionInit) initializeContainerEnvironment(ctx context.Context, sessionID int64, taskID string, imageConfig *dao_obj_image.ObjImage) error {
	log.Printf("开始初始化容器环境: session_id=%d, task_id=%s, image_id=%s", sessionID, taskID, imageConfig.ImageID)

	// 如果没有指定环境ID，跳过环境初始化
	if s.InputData.EnvID == 0 {
		log.Printf("未指定环境ID，跳过环境初始化")
		return nil
	}

	// 1. 获取环境配置
	env, err := dao_mcp_env.McpEnvBusinessIns.SelectByPrimaryKey(ctx, s.InputData.EnvID)
	if err != nil {
		return fmt.Errorf("获取环境配置失败: %w", err)
	}

	// 2. 解析环境依赖配置
	if env.EnvDependency == nil {
		log.Printf("环境依赖配置为空，跳过初始化")
		return nil
	}

	// 从JSONData中提取environment_dependency
	envDependencyData, ok := env.EnvDependency["environment_dependency"]
	if !ok {
		log.Printf("环境依赖配置中没有environment_dependency字段，跳过初始化")
		return nil
	}

	// 将interface{}转换为环境依赖列表
	var envDeps []struct {
		Path    string `json:"path"`
		Type    string `json:"type"`
		Content string `json:"content"`
	}

	// 先转换为JSON字节，再解析
	envDepsBytes, err := json.Marshal(envDependencyData)
	if err != nil {
		return fmt.Errorf("序列化环境依赖配置失败: %w", err)
	}

	err = json.Unmarshal(envDepsBytes, &envDeps)
	if err != nil {
		return fmt.Errorf("解析环境依赖配置失败: %w", err)
	}

	// 3. 逐个处理环境依赖
	for i, dep := range envDeps {
		log.Printf("处理环境依赖 %d/%d: type=%s, path=%s", i+1, len(envDeps), dep.Type, dep.Path)

		err := s.processEnvironmentDependency(ctx, sessionID, taskID, dep)
		if err != nil {
			return fmt.Errorf("处理环境依赖失败 [%d] type=%s path=%s content=%s: %w", i+1, dep.Type, dep.Path, dep.Content, err)
		}
	}

	log.Printf("容器环境初始化完成: session_id=%d", sessionID)
	return nil
}

// processEnvironmentDependency 处理单个环境依赖项
func (s *SessionInit) processEnvironmentDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	log.Printf("处理环境依赖: type=%s, path=%s", dep.Type, dep.Path)

	switch dep.Type {
	case "file":
		return s.processFileDependency(ctx, sessionID, taskID, dep)
	case "directory":
		return s.processDirectoryDependency(ctx, sessionID, taskID, dep)
	case "db":
		return s.processDatabaseDependency(ctx, sessionID, taskID, dep)
	case "url":
		return s.processURLDependency(ctx, sessionID, taskID, dep)
	case "cmd":
		return s.processCommandDependency(ctx, sessionID, taskID, dep)
	case "delay_cmd":
		log.Printf("delay_cmd类型暂时不处理: path=%s", dep.Path)
		return nil
	default:
		return fmt.Errorf("不支持的环境依赖类型: %s", dep.Type)
	}
}

// processFileDependency 处理文件类型的环境依赖
func (s *SessionInit) processFileDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	log.Printf("处理文件依赖: path=%s", dep.Path)

	// 1. 生成BOS key，格式为：session_env_init/session_id/filename
	fileName := strings.TrimPrefix(dep.Path, "./")
	if after, ok := strings.CutPrefix(fileName, "/"); ok {
		fileName = after
	}
	bosKey := fmt.Sprintf("session_env_init/%d/%s", sessionID, fileName)

	// 2. 上传文件内容到BOS
	err := rpc_bos.UploadObjectFromString(ctx, bosKey, dep.Content, "")
	if err != nil {
		return fmt.Errorf("上传文件到BOS失败: %w", err)
	}

	// 3. 生成永久链接
	bosURL := rpc_bos.GenerateVisitURL(ctx, bosKey, -1)

	// 4. 通过k8s_client上传文件到容器
	uploadReq := &rpc_k8s_proxy.UploadFileRequest{
		SourceType: "mcp",
		JobName:    taskID,
		FileInfo: []rpc_k8s_proxy.FileInfo{
			{
				FileURL:  bosURL,
				DestPath: dep.Path,
			},
		},
	}

	_, err = rpc_k8s_proxy.K8sProxyClientIns.UploadFile(ctx, uploadReq)
	if err != nil {
		return fmt.Errorf("上传文件到容器失败: %w", err)
	}

	log.Printf("文件依赖处理完成: path=%s, bos_key=%s", dep.Path, bosKey)
	return nil
}

// processDirectoryDependency 处理目录类型的环境依赖
func (s *SessionInit) processDirectoryDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	log.Printf("处理目录依赖: path=%s", dep.Path)

	// 直接调用容器内环境启动，自动创建父目录
	// 使用mkdir -p命令创建目录
	execReq := &rpc_k8s_proxy.ExecCommandRequest{
		SourceType: "mcp",
		JobName:    taskID,
		Command:    []string{"mkdir", "-p", dep.Path},
	}

	_, err := rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, execReq)
	if err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	log.Printf("目录依赖处理完成: path=%s", dep.Path)
	return nil
}

// processDatabaseDependency 处理数据库类型的环境依赖
func (s *SessionInit) processDatabaseDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	log.Printf("处理数据库依赖: path=%s", dep.Path)

	// 1. 使用现有的数据库初始化逻辑
	envDep := environment.EnvironmentDependency{
		Path:    dep.Path,
		Type:    dep.Type,
		Content: dep.Content,
	}

	// 2. 创建数据库策略并初始化
	dbStrategy := environment.NewDatabaseStrategy()
	result, err := dbStrategy.Initialize(ctx, envDep)
	if err != nil || !result.Success {
		return fmt.Errorf("数据库初始化失败: %w", err)
	}

	// 3. 生成BOS key并上传数据库文件
	fileName := strings.TrimPrefix(dep.Path, "./")
	if after, ok := strings.CutPrefix(fileName, "/"); ok {
		fileName = after
	}
	bosKey := fmt.Sprintf("session_env_init/%d/%s", sessionID, fileName)

	// 4. 上传数据库文件到BOS
	file, err := os.Open(dep.Path)
	if err != nil {
		return fmt.Errorf("打开数据库文件失败: %w", err)
	}
	defer file.Close()

	err = rpc_bos.UploadObjectFromStream(ctx, bosKey, file)
	if err != nil {
		return fmt.Errorf("上传数据库文件到BOS失败: %w", err)
	}

	// 5. 生成永久链接
	bosURL := rpc_bos.GenerateVisitURL(ctx, bosKey, -1)

	// 6. 通过k8s_client上传文件到容器
	uploadReq := &rpc_k8s_proxy.UploadFileRequest{
		SourceType: "mcp",
		JobName:    taskID,
		FileInfo: []rpc_k8s_proxy.FileInfo{
			{
				FileURL:  bosURL,
				DestPath: dep.Path,
			},
		},
	}

	_, err = rpc_k8s_proxy.K8sProxyClientIns.UploadFile(ctx, uploadReq)
	if err != nil {
		return fmt.Errorf("上传数据库文件到容器失败: %w", err)
	}

	log.Printf("数据库依赖处理完成: path=%s, bos_key=%s", dep.Path, bosKey)
	return nil
}

// processURLDependency 处理URL类型的环境依赖
func (s *SessionInit) processURLDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	log.Printf("处理URL依赖: path=%s, url=%s", dep.Path, dep.Content)

	// 直接调用k8s_client upload_file，使用URL作为文件源
	uploadReq := &rpc_k8s_proxy.UploadFileRequest{
		SourceType: "mcp",
		JobName:    taskID,
		FileInfo: []rpc_k8s_proxy.FileInfo{
			{
				FileURL:  dep.Content, // URL作为文件源
				DestPath: dep.Path,
			},
		},
	}

	_, err := rpc_k8s_proxy.K8sProxyClientIns.UploadFile(ctx, uploadReq)
	if err != nil {
		return fmt.Errorf("从URL下载文件到容器失败: %w", err)
	}

	log.Printf("URL依赖处理完成: path=%s, url=%s", dep.Path, dep.Content)
	return nil
}

// processCommandDependency 处理命令类型的环境依赖
func (s *SessionInit) processCommandDependency(ctx context.Context, sessionID int64, taskID string, dep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}) error {
	command := []string{"sh", "-c", dep.Content}
	execReq := &rpc_k8s_proxy.ExecCommandRequest{
		SourceType: "mcp",
		JobName:    taskID,
		Command:    command,
		Timeout:    60,
	}

	result, err := rpc_k8s_proxy.K8sProxyClientIns.ExecCommand(ctx, execReq)
	if result.Code != 0 {
		return fmt.Errorf("执行命令失败: code: %d, stdout: %s, stderr: %s", result.Code, result.Stdout, result.Stderr)
	}
	if err != nil {
		return fmt.Errorf("执行命令失败: %w", err)
	}

	resource.LoggerService.Notice(ctx,
		fmt.Sprintf("session_init [id=%d] 命令依赖处理完成: command=%s, stdout=%s, stderr=%s", sessionID, dep.Content, result.Stdout, result.Stderr))
	return nil
}
