package session

import (
	"context"
	"fmt"
	"log"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/reward_calculator"
	rpc_k8s_proxy "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/rpc_k8s_proxy"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionStopInputData Session停止输入数据
type SessionStopInputData struct {
	SessionID   int64  `json:"session_id,omitempty"`
	SessionCode string `json:"session_code,omitempty"`
}

// SessionStopOutputData Session停止输出数据
type SessionStopOutputData struct {
	SessionID int64  `json:"session_id"`
	Status    string `json:"status"`
}

// SessionStop Session停止服务
type SessionStop struct {
	base.Service[SessionStopInputData, SessionStopOutputData]
}

// Execute 执行Session停止逻辑
func (s *SessionStop) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData
	var session *dao_session.ObjSession

	// 1. 根据输入参数获取Session
	if input.SessionCode != "" {
		session, err = dao_session.SessionBusinessIns.GetSessionByCode(ctx, input.SessionCode)
		if err != nil {
			log.Printf("通过SessionCode查询Session失败: %v", err)
			return nil, nil, err
		}
	} else if input.SessionID != 0 {
		session, err = dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
		if err != nil {
			log.Printf("通过SessionID查询Session失败: %v", err)
			return nil, nil, err
		}
	} else {
		return nil, nil, &lib_error.CustomErr{Code: errcode.InvalidInput, Msg: "session_id或session_code必须提供一个"}
	}

	sessionID := session.SessionID

	// 2. 检查Session状态
	if session.ContainerStatus == dao_session.ContainerStatusStopped {
		log.Printf("Session已经停止: session_id=%d", sessionID)
		output := &SessionStopOutputData{
			SessionID: sessionID,
			Status:    "stopped",
		}
		return output, nil, nil
	}

	// 如果已经是stopping状态，直接返回（幂等性）
	if session.ContainerStatus == dao_session.ContainerStatusStopping {
		log.Printf("Session正在停止中: session_id=%d", sessionID)
		output := &SessionStopOutputData{
			SessionID: sessionID,
			Status:    "stopping",
		}
		return output, nil, nil
	}

	// 3. 先更新状态为stopping
	err = dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
		"container_status": dao_session.ContainerStatusStopping,
	})
	if err != nil {
		log.Printf("更新Session状态为stopping失败: %v", err)
		return nil, nil, err
	}
	metrics_helper.RecordSessionStopping()

	// 4. 启动异步任务执行停止逻辑
	go s.asyncStopSession(context.Background(), sessionID, session)

	// 5. 返回成功结果
	output := &SessionStopOutputData{
		SessionID: sessionID,
		Status:    "stopping",
	}

	return output, nil, nil
}

// asyncStopSession 异步执行session停止逻辑
func (s *SessionStop) asyncStopSession(ctx context.Context, sessionID int64, session *dao_session.ObjSession) {
	// 1. 计算奖励值
	if session.ImageID != "" && session.Reward == nil {
		calculator := reward_calculator.NewRewardCalculator()
		rewardJSON, err := calculator.CalculateReward(ctx, session, "", 300)
		if err != nil {
			resource.LoggerService.Warning(ctx, fmt.Sprintf("计算奖励失败: session_id=%d, error=%v", sessionID, err))
			// 计算奖励失败不影响后续流程，继续执行
		}
		// 2. 更新reward字段（存储JSON数据）
		if rewardJSON != "" {
			err := dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
				"reward": rewardJSON,
			})
			if err != nil {
				resource.LoggerService.Error(ctx, fmt.Sprintf("更新reward字段失败: session_id=%d, error=%v", sessionID, err))
			}
		}
	}

	// 3. 记录会话日志
	if session.JobID != "" {
		err := dao_session.SessionBusinessIns.SessionLogRecord(ctx, sessionID, session.JobID)
		if err != nil {
			resource.LoggerService.Error(ctx, fmt.Sprintf("Session日志记录失败: session_id=%d, error=%v", sessionID, err))
		}

		// 4. 调用k8s删除容器
		err = rpc_k8s_proxy.K8sProxyClientIns.DeleteJob(ctx, session.JobID)
		if err != nil {
			errMsg := fmt.Sprintf("删除容器失败: %v", err)
			resource.LoggerService.Error(ctx, fmt.Sprintf("删除容器失败: session_id=%d, error=%s", sessionID, errMsg))
			// 删除容器失败，但仍然更新状态为stopped，并记录错误信息
			updateErr := dao_session.SessionBusinessIns.UpdateStatusWithError(ctx, sessionID, dao_session.ContainerStatusStopped, errMsg)
			if updateErr != nil {
				resource.LoggerService.Error(ctx, fmt.Sprintf("更新Session状态失败: session_id=%d, error=%v", sessionID, updateErr))
				return
			}
		} else {
			// 5. 更新状态为stopped
			err = dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
				"container_status": dao_session.ContainerStatusStopped,
				"stopped_at":       time.Now(),
			})
			if err != nil {
				resource.LoggerService.Error(ctx, fmt.Sprintf("更新Session状态失败: session_id=%d, error=%v", sessionID, err))
				return
			}
		}
	} else {
		resource.LoggerService.Notice(ctx, fmt.Sprintf("session_stop [id=%d] Session没有关联的容器任务，直接更新状态", sessionID))
		err := dao_session.SessionBusinessIns.UpdateMap(ctx, sessionID, map[string]any{
			"container_status": dao_session.ContainerStatusStopped,
			"stopped_at":       time.Now(),
		})
		if err != nil {
			resource.LoggerService.Error(ctx, fmt.Sprintf("更新Session状态失败: session_id=%d, error=%v", sessionID, err))
			return
		}
	}

	metrics_helper.RecordSessionStopped()
}
