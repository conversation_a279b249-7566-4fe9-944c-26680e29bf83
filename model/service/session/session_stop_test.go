package session_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	dao_base "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
	service "icode.baidu.com/baidu/dataeng/mcp-online-server/model/service/session"
)

func TestSessionStop_Execute_Success(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_stop_001",
		SessionCode:     fmt.Sprintf("test_session_stop_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusRunning,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 执行停止操作
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionID: sessionID,
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证返回结果
	output, ok := resp.(*service.SessionStopOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, "stopped", output.Status)

	// 验证数据库状态已更新
	updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	assert.NoError(t, err)
	assert.NotNil(t, updatedSession)
	assert.Equal(t, dao_session.ContainerStatusStopped, updatedSession.ContainerStatus)
	assert.NotNil(t, updatedSession.StoppedAt)
}

func TestSessionStop_Execute_SessionNotFound(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 使用不存在的SessionID
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionID: 99999,
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "Session不存在")
}

func TestSessionStop_Execute_AlreadyStopped(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建已停止的Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_stop_002",
		EnvID:           1001,
		SessionCode:     fmt.Sprintf("test_session_stop_already_stopped_%d", time.Now().UnixNano()),
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusStopped,
		StoppedAt:       &[]time.Time{time.Now()}[0],
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 执行停止操作
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionID: sessionID,
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证返回结果
	output, ok := resp.(*service.SessionStopOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, "stopped", output.Status)
}

func TestSessionStop_Execute_NoJobID(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建没有JobID的Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "", // 空JobID
		SessionCode:     fmt.Sprintf("test_session_no_job_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusInit,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 执行停止操作
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionID: sessionID,
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证返回结果
	output, ok := resp.(*service.SessionStopOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, "stopped", output.Status)

	// 验证数据库状态已更新
	updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	assert.NoError(t, err)
	assert.NotNil(t, updatedSession)
	assert.Equal(t, dao_session.ContainerStatusStopped, updatedSession.ContainerStatus)
	assert.NotNil(t, updatedSession.StoppedAt)
}

// TestSessionStop_Execute_AsyncStopping 测试异步停止逻辑
func TestSessionStop_Execute_AsyncStopping(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_stop_async_001",
		SessionCode:     fmt.Sprintf("test_session_stop_async_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusRunning,
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 执行停止操作
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionCode: session.SessionCode, // 使用SessionCode而不是SessionID
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证返回结果
	output, ok := resp.(*service.SessionStopOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, "stopping", output.Status) // 应该返回stopping状态

	// 验证数据库中的状态已更新为stopping
	updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	assert.NoError(t, err)
	assert.NotNil(t, updatedSession)
	assert.Equal(t, dao_session.ContainerStatusStopping, updatedSession.ContainerStatus)

	// 等待一段时间让异步任务执行（在实际测试中，异步任务可能会被mock）
	time.Sleep(100 * time.Millisecond)
}

// TestSessionStop_Execute_AlreadyStopping 测试重复调用stopping状态的session
func TestSessionStop_Execute_AlreadyStopping(t *testing.T) {
	setUpAll(t)
	defer tearDownAll(t)

	// 创建测试Session，状态为stopping
	serverIds := dao_base.JSONArray[int64]{1, 2, 3}
	mcpTools := dao_base.JSONData{
		"filesystem": map[string]interface{}{
			"name":        "filesystem",
			"description": "文件系统操作工具",
		},
	}

	session := &dao_session.ObjSession{
		JobID:           "job_test_session_stop_stopping_001",
		SessionCode:     fmt.Sprintf("test_session_stop_stopping_%d", time.Now().UnixNano()),
		EnvID:           1001,
		ServerIds:       serverIds,
		McpTools:        mcpTools,
		ContainerStatus: dao_session.ContainerStatusStopping, // 已经是stopping状态
	}

	sessionID, err := dao_session.SessionBusinessIns.Insert(ctx, session)
	assert.NoError(t, err)
	assert.Greater(t, sessionID, int64(0))

	// 执行停止操作
	stopService := &service.SessionStop{}
	stopService.InputData = &service.SessionStopInputData{
		SessionCode: session.SessionCode, // 使用SessionCode而不是SessionID
	}

	resp, _, err := stopService.Execute(ctx, nil)
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// 验证返回结果
	output, ok := resp.(*service.SessionStopOutputData)
	assert.True(t, ok)
	assert.Equal(t, sessionID, output.SessionID)
	assert.Equal(t, "stopping", output.Status) // 应该返回stopping状态

	// 验证数据库中的状态仍然是stopping
	updatedSession, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, sessionID)
	assert.NoError(t, err)
	assert.NotNil(t, updatedSession)
	assert.Equal(t, dao_session.ContainerStatusStopping, updatedSession.ContainerStatus)
}
