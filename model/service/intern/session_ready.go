package intern

import (
	"context"
	"fmt"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/types"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/utils"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionReadyInputData Session就绪通知输入数据
type SessionReadyInputData struct {
	SessionID int64           `json:"session_id" validate:"required"` // Session ID
	MCPTools  []types.MCPTool `json:"mcp_tools" validate:"required"`  // 可用的MCP工具列表
	Message   string          `json:"message,omitempty"`              // 附加消息
}

// SessionReadyOutputData Session就绪通知输出数据
type SessionReadyOutputData struct {
	Success   bool   `json:"success"`    // 是否成功处理
	SessionID int64  `json:"session_id"` // Session ID
	Message   string `json:"message"`    // 处理结果消息
}

// SessionReady Session就绪通知服务
type SessionReady struct {
	base.Service[SessionReadyInputData, SessionReadyOutputData]
}

// Execute 执行Session就绪通知逻辑
func (s *SessionReady) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	// TODO: 从框架获取输入数据，目前先使用示例数据进行测试
	input := s.InputData

	// 1. 验证Session是否存在
	session, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询Session失败: %v", err))
		return nil, nil, err
	}

	// 3. 将MCPTool列表转换为JSONData格式
	mcpToolsJSON, err := utils.ConvertMCPToolsToJSONData(input.MCPTools)
	if err != nil {
		return nil, nil, &lib_error.CustomErr{Code: errcode.SysFileCreateError, Msg: fmt.Sprintf("转换工具列表失败: %v", err)}
	}

	err = dao_session.SessionBusinessIns.UpdateMcpToolsAndStatus(ctx, session.SessionID, mcpToolsJSON, dao_session.ContainerStatusRunning)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("更新Session失败: %v", err))
		return nil, nil, err
	}

	// 5. 返回成功结果
	output := &SessionReadyOutputData{
		Success:   true,
		SessionID: input.SessionID,
		Message:   "Session就绪通知处理成功",
	}

	return output, nil, nil
}
