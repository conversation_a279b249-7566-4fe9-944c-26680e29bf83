package intern

import (
	"context"
	"fmt"
	"time"

	base "icode.baidu.com/baidu/dataeng/data-gdp-library/base/model/service"
	lib_error "icode.baidu.com/baidu/dataeng/data-gdp-library/types/error"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/errcode"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/metrics_helper"
	"icode.baidu.com/baidu/dataeng/mcp-online-server/library/resource"
	dao_session "icode.baidu.com/baidu/dataeng/mcp-online-server/model/dao/session"
)

// SessionStopInputData Session停止输入数据
type SessionStopInputData struct {
	SessionID int64  `json:"session_id" validate:"required"`
	Message   string `json:"message"`
}

// SessionStopOutputData Session停止输出数据
type SessionStopOutputData struct {
	Success   bool   `json:"success"`
	SessionID int64  `json:"session_id"`
	Message   string `json:"message"`
}

// SessionStop Session停止服务（内部API）
type SessionStop struct {
	base.Service[SessionStopInputData, SessionStopOutputData]
}

// Execute 执行Session停止逻辑
func (s *SessionStop) Execute(ctx context.Context, req ghttp.Request) (ret any, ext any, err error) {
	input := s.InputData

	resource.LoggerService.Notice(ctx, fmt.Sprintf("开始停止Session（内部API）: session_id=%d", input.SessionID))

	// 1. 验证Session是否存在
	session, err := dao_session.SessionBusinessIns.SelectByPrimaryKey(ctx, input.SessionID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("查询Session失败: %v", err))
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.SQLSelectError,
			Msg:  fmt.Sprintf("查询Session失败: %v", err),
		}
	}

	// 2. 检查Session状态 - 如果已经停止，直接返回成功（幂等性）
	if session.ContainerStatus == dao_session.ContainerStatusStopped {
		resource.LoggerService.Notice(ctx, fmt.Sprintf("Session已经停止: session_id=%d", input.SessionID))
		output := &SessionStopOutputData{
			Success:   true,
			SessionID: input.SessionID,
			Message:   "Session已经停止",
		}
		return output, nil, nil
	}

	// 3. 记录会话日志到BOS
	err = dao_session.SessionBusinessIns.SessionLogRecord(ctx, input.SessionID, session.JobID)
	if err != nil {
		resource.LoggerService.Warning(ctx, fmt.Sprintf("记录会话日志到BOS失败: %v", err))
	}

	// 5. 更新Session状态为停止
	updateData := map[string]any{
		"container_status": dao_session.ContainerStatusStopped,
		"err_msg":          input.Message,
		"stopped_at":       time.Now(),
	}

	err = dao_session.SessionBusinessIns.UpdateMap(ctx, input.SessionID, updateData)
	if err != nil {
		resource.LoggerService.Error(ctx, fmt.Sprintf("更新Session状态失败: session_id=%d, error=%v", input.SessionID, err))
		return nil, nil, &lib_error.CustomErr{
			Code: errcode.SQLUpdateError,
			Msg:  fmt.Sprintf("更新Session状态失败: %v", err),
		}
	}
	// Record metrics for session stopped
	metrics_helper.RecordSessionStopped()

	// 6. 构建返回结果
	output := &SessionStopOutputData{
		Success:   true,
		SessionID: input.SessionID,
		Message:   "Session停止成功",
	}
	return output, nil, nil
}
